# gui/dialogs.py

from typing import Optional
import logging
import sys
import os
import math
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('QtAgg') # Ensure Qt backend is used
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from datetime import datetime

from PIL import Image

from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt, QSize, QPoint, QRect, QPointF, QRectF, Slot, QTimer
from PySide6.QtGui import QPixmap, QImage, QIcon, QPainter, QPen, QColor, QPalette
from PySide6.QtWidgets import (QDialog, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                               QDialogButtonBox, QCheckBox, QGridLayout, QTabWidget,
                               QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
                               QMessageBox, QGraphicsRectItem, QLabel, QSpinBox,
                               QFileDialog, QRadioButton, QGroupBox) # Added QRadioButton, QGroupBox

# Assuming utils.py is in the same directory (gui)
from .utils import pil_image_to_qimage, resource_path, apply_stylesheet

logger = logging.getLogger(__name__)

# --- Plotting Dialogs ---

class PlotSelectionDialog(QDialog):
    """Dialog to select parameters for plotting."""
    def __init__(self, df: pd.DataFrame, parent=None):
        super().__init__(parent)
        self.df = df
        self.selected_params = []
        self.setWindowTitle("Select Parameters to Plot")
        self.setMinimumWidth(400)

        layout = QVBoxLayout(self)

        # Filter columns for plotting
        numeric_cols = self.df.select_dtypes(include=np.number).columns.tolist()
        cols_to_plot = [col for col in numeric_cols if col not in ['Center_X (px)', 'Center_Y (px)', 'Object ID']] # Exclude IDs/Coords

        if not cols_to_plot:
            layout.addWidget(QtWidgets.QLabel("No numeric parameters found in results to plot."))
            self.checkboxes = {} # Ensure checkboxes exists even if empty
        else:
            grid_layout = QGridLayout()
            layout.addLayout(grid_layout)
            self.checkboxes = {}
            num_cols = 3
            row, col = 0, 0
            for column in cols_to_plot:
                checkbox = QCheckBox(column)
                checkbox.setChecked(False)
                grid_layout.addWidget(checkbox, row, col)
                self.checkboxes[column] = checkbox
                col += 1
                if col >= num_cols: col = 0; row += 1

        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_selection)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Apply parent's stylesheet/theme if possible
        if parent:
             self.setStyleSheet(parent.styleSheet())
             self.setPalette(parent.palette())


    def accept_selection(self):
        self.selected_params = [param for param, cb in self.checkboxes.items() if cb.isChecked()]
        if not self.selected_params and self.checkboxes: # Don't reject if no columns were available
             QMessageBox.information(self, "No Selection", "No parameters were selected for plotting.")
             # Don't accept, let user select again or cancel
             # self.reject() # Or reject if nothing selected
        else:
            logger.info(f"Plot parameters selected: {self.selected_params}")
            self.accept() # Close dialog with accept code

    def get_selected_parameters(self) -> list:
        return self.selected_params


class PlotDisplayDialog(QDialog):
    """Dialog to display various plots in tabs."""
    def __init__(self, df: pd.DataFrame, selected_params: list, parent=None):
        super().__init__(parent)
        self.df = df
        self.selected_params = selected_params
        self.plot_widgets_info = [] # Store tuples: (widget, canvas, figure, title)

        self.setWindowTitle("Analysis Plots")
        self.resize(900, 700)

        layout = QVBoxLayout(self)
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Apply parent's theme/style
        if parent:
            self.setStyleSheet(parent.styleSheet())
            self.setPalette(parent.palette())

        self.create_plots()

        # --- Save Button ---
        button_layout = QHBoxLayout()
        save_button = QPushButton(QIcon(resource_path("icons/save.png")), " Save All Plots")
        save_button.clicked.connect(self.save_plots)
        button_layout.addStretch()
        button_layout.addWidget(save_button)
        layout.addLayout(button_layout)

        self.finished.connect(self.cleanup_resources) # Cleanup on close


    def create_plot_widget_base(self) -> tuple:
         """Creates the basic structure (widget, layout, figure, canvas, axes) for a plot tab."""
         widget = QWidget()
         layout = QVBoxLayout(widget)
         # Use constrained_layout for better spacing
         fig, ax = plt.subplots(figsize=(8, 6), constrained_layout=True)

         # Apply theme colors from the dialog's palette
         face_color = self.palette().color(QPalette.ColorRole.Window).name()
         text_color = self.palette().color(QPalette.ColorRole.WindowText).name()
         grid_color = self.palette().color(QPalette.ColorRole.AlternateBase).name()

         fig.patch.set_facecolor(face_color)
         ax.set_facecolor(face_color)
         # Set colors for axes elements
         ax.tick_params(axis='x', colors=text_color)
         ax.tick_params(axis='y', colors=text_color)
         ax.xaxis.label.set_color(text_color)
         ax.yaxis.label.set_color(text_color)
         ax.title.set_color(text_color)
         # Set spine colors
         for spine in ax.spines.values():
              spine.set_edgecolor(text_color)

         canvas = FigureCanvas(fig)
         layout.addWidget(canvas)
         widget.setLayout(layout)
         return widget, canvas, fig, ax, grid_color # Return grid color too


    def create_plots(self):
        """Creates and adds all selected plot types as tabs."""
        if self.df is None or self.df.empty or not self.selected_params:
            logger.warning("Cannot create plots: No data or parameters selected.")
            return

        # --- Distribution Plot ---
        try:
            widget, canvas, fig, ax, grid_color = self.create_plot_widget_base()
            num_plots = len(self.selected_params)
            colors = plt.cm.viridis(np.linspace(0, 1, num_plots))
            max_bins = 30

            for i, column in enumerate(self.selected_params):
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                     # Auto-binning (simplified)
                     q75, q25 = np.percentile(numeric_data, [75 ,25])
                     iqr = q75 - q25
                     bin_width = 2 * iqr / (len(numeric_data) ** (1/3)) if iqr > 0 else 0
                     num_bins = int(np.ceil((numeric_data.max() - numeric_data.min()) / bin_width)) if bin_width > 0 else 10
                     num_bins = min(num_bins, max_bins)
                     if num_bins <= 1: num_bins = 10

                     ax.hist(numeric_data, bins=num_bins, alpha=0.75, label=column, color=colors[i])

            ax.set_xlabel('Value')
            ax.set_ylabel('Frequency')
            ax.set_title('Parameter Distributions (Histograms)')
            if num_plots <= 10: ax.legend()
            else: ax.legend(ncol=2, fontsize='small')
            ax.grid(True, linestyle=':', alpha=0.6, color=grid_color)
            # fig.tight_layout() # Use constrained_layout instead
            canvas.draw()
            self.tab_widget.addTab(widget, "Histograms")
            self.plot_widgets_info.append((widget, canvas, fig, "Histograms"))
        except Exception as e: logger.exception(f"Error creating histogram plot: {e}")

        # --- Cumulative Frequency Plot ---
        try:
            widget, canvas, fig, ax, grid_color = self.create_plot_widget_base()
            num_plots = len(self.selected_params)
            colors = plt.cm.viridis(np.linspace(0, 1, num_plots))

            for i, column in enumerate(self.selected_params):
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                    sorted_data = np.sort(numeric_data)
                    yvals = np.arange(1, len(sorted_data) + 1) / float(len(sorted_data))
                    ax.plot(sorted_data, yvals, label=column, color=colors[i], marker='.', markersize=2, linestyle='-', linewidth=1.5)

            ax.set_xlabel('Value')
            ax.set_ylabel('Cumulative Frequency')
            ax.set_title('Cumulative Frequency Distributions')
            ax.grid(True, linestyle=':', alpha=0.6, color=grid_color)
            if num_plots <= 10: ax.legend()
            else: ax.legend(ncol=2, fontsize='small')
            # fig.tight_layout()
            canvas.draw()
            self.tab_widget.addTab(widget, "Cumulative Frequency")
            self.plot_widgets_info.append((widget, canvas, fig, "Cumulative_Frequency"))
        except Exception as e: logger.exception(f"Error creating cumulative frequency plot: {e}")

        # --- Box Plot ---
        try:
            widget, canvas, fig, ax, grid_color = self.create_plot_widget_base()
            data_to_plot = []
            labels = []
            for column in self.selected_params:
                numeric_data = pd.to_numeric(self.df[column], errors='coerce').dropna()
                if not numeric_data.empty:
                    data_to_plot.append(numeric_data)
                    labels.append(column)

            if data_to_plot:
                boxplot = ax.boxplot(data_to_plot, labels=labels, patch_artist=True, vert=True, showfliers=True)
                num_boxes = len(data_to_plot)
                colors = plt.cm.viridis(np.linspace(0, 1, num_boxes))
                text_color = self.palette().color(QPalette.ColorRole.WindowText).name()

                for patch, color in zip(boxplot['boxes'], colors):
                    patch.set_facecolor(color); patch.set_alpha(0.7)
                for median in boxplot['medians']: median.set_color('red'); median.set_linewidth(1.5)
                for whisker in boxplot['whiskers']: whisker.set_color(text_color); whisker.set_linestyle('--')
                for cap in boxplot['caps']: cap.set_color(text_color)
                for flier in boxplot['fliers']: flier.set_markerfacecolor(text_color); flier.set_markeredgecolor(text_color); flier.set_alpha(0.5); flier.set_marker('.')

                ax.set_ylabel('Value')
                ax.set_title('Parameter Box Plots')
                ax.yaxis.grid(True, linestyle=':', alpha=0.6, color=grid_color)
                plt.setp(ax.get_xticklabels(), rotation=30, ha='right') # Rotate labels
                # fig.tight_layout() # constrained_layout preferred
                canvas.draw()
                self.tab_widget.addTab(widget, "Box Plots")
                self.plot_widgets_info.append((widget, canvas, fig, "Box_Plots"))
        except Exception as e: logger.exception(f"Error creating box plot: {e}")

        # --- Scatter Plot Example (Area vs Elongation) ---
        # Add more options if desired
        param_x, param_y = 'Area (µm²)', 'Elongation'
        if param_x in self.selected_params and param_y in self.selected_params:
            try:
                widget, canvas, fig, ax, grid_color = self.create_plot_widget_base()
                data_x = pd.to_numeric(self.df[param_x], errors='coerce')
                data_y = pd.to_numeric(self.df[param_y], errors='coerce')
                valid_mask = data_x.notna() & data_y.notna()
                data_x = data_x[valid_mask]
                data_y = data_y[valid_mask]

                if not data_x.empty:
                     # Optional: Color by a third parameter (e.g., Solidity)
                     color_param = 'Solidity'
                     colors = None
                     cbar = None
                     if color_param in self.selected_params:
                         color_data = pd.to_numeric(self.df.loc[valid_mask, color_param], errors='coerce').fillna(0)
                         norm = plt.Normalize(vmin=color_data.min(), vmax=color_data.max())
                         mapper = plt.cm.ScalarMappable(norm=norm, cmap='viridis')
                         colors = mapper.to_rgba(color_data)
                         # Create colorbar
                         cbar = fig.colorbar(mapper, ax=ax)
                         cbar.set_label(color_param)
                         # Set colorbar tick/label colors
                         cbar.ax.yaxis.set_tick_params(color=text_color)
                         plt.setp(plt.getp(cbar.ax.axes, 'yticklabels'), color=text_color)
                         cbar.set_label(color_param, color=text_color)


                     scatter = ax.scatter(data_x, data_y, alpha=0.6, s=15, c=colors)
                     ax.set_xlabel(param_x); ax.set_ylabel(param_y)
                     ax.set_title(f'{param_y} vs {param_x}')
                     ax.grid(True, linestyle=':', alpha=0.6, color=grid_color)
                     # fig.tight_layout()
                     canvas.draw()
                     tab_title = f"{param_y} vs {param_x}"
                     self.tab_widget.addTab(widget, tab_title)
                     self.plot_widgets_info.append((widget, canvas, fig, tab_title.replace(" ","_")))

            except Exception as e: logger.exception(f"Error creating scatter plot ({param_y} vs {param_x}): {e}")


    def save_plots(self):
        """Saves all plots currently displayed in the tabs."""
        if not self.plot_widgets_info:
            QMessageBox.warning(self, "No Plots", "No plots to save.")
            return

        # Use parent's last_save_dir if available, otherwise home directory
        parent = self.parent()
        start_dir = getattr(parent, 'last_save_dir', os.path.expanduser("~"))

        directory = QFileDialog.getExistingDirectory(self, "Select Directory to Save Plots", start_dir)
        if not directory: return

        if parent and hasattr(parent, 'last_save_dir'):
            parent.last_save_dir = directory # Update parent's last used directory

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = os.path.splitext(getattr(parent, 'image_filename', 'plot'))[0]
        saved_count = 0
        errors = []

        for widget, canvas, fig, title in self.plot_widgets_info:
            try:
                 filename = f"{base_filename}_{title}_{timestamp}.png"
                 file_path = os.path.join(directory, filename)
                 # Use figure's facecolor and save with high DPI
                 fig.savefig(file_path, dpi=300, bbox_inches='tight', facecolor=fig.get_facecolor())
                 logger.info(f"Saved plot: {file_path}")
                 saved_count += 1
            except Exception as e:
                 error_msg = f"Failed to save plot '{title}': {e}"
                 logger.error(error_msg, exc_info=True)
                 errors.append(error_msg)

        if saved_count > 0:
            msg = f"{saved_count} plot(s) saved successfully to:\n{directory}"
            if errors: msg += f"\n\nErrors encountered:\n" + "\n".join(errors)
            QMessageBox.information(self, "Plots Saved", msg)
            # Optional: Open directory
            try:
                if sys.platform == 'win32': os.startfile(directory)
                elif sys.platform == 'darwin': os.system(f'open "{directory}"')
                else: os.system(f'xdg-open "{directory}"')
            except Exception as e: logger.warning(f"Could not open directory automatically: {e}")
        else:
            self._show_error_message("Save Error", "Failed to save any plots.\n" + "\n".join(errors))

    @Slot()
    def cleanup_resources(self):
        """Closes matplotlib figures when the dialog is closed."""
        logger.debug(f"Cleaning up resources for {len(self.plot_widgets_info)} plots.")
        for widget, canvas, fig, title in self.plot_widgets_info:
            try:
                plt.close(fig) # Close the matplotlib figure
            except Exception as e:
                logger.error(f"Error closing plot figure '{title}': {e}")
        self.plot_widgets_info.clear() # Clear the list

    def _show_error_message(self, title, message):
         # Use parent's method if available for consistent error display
         parent = self.parent()
         if parent and hasattr(parent, '_show_error_message'):
              parent._show_error_message(title, message)
         else:
              QMessageBox.critical(self, title, message)


# --- Function to Show Plotting Dialog ---
def show_plot_selection_dialog(df: pd.DataFrame, parent=None) -> Optional[PlotDisplayDialog]:
    """Shows the parameter selection dialog, then the plot display dialog if parameters selected."""
    selection_dialog = PlotSelectionDialog(df, parent)
    result = selection_dialog.exec() # Show modally

    if result == QDialog.Accepted:
        selected_params = selection_dialog.get_selected_parameters()
        if selected_params:
            plot_dialog = PlotDisplayDialog(df, selected_params, parent)
            # plot_dialog.show() # Show modelessly - requires tracking in main window
            return plot_dialog # Return the dialog for the caller to manage/show
    return None


# --- Cropping Dialog ---

class CropDialog(QDialog):
    """Dialog for selecting a crop area on an image."""
    def __init__(self, image: Image.Image, parent=None):
        super().__init__(parent)
        self.image = image
        self.crop_rect_scene: Optional[QRectF] = None
        self.cropped_image: Optional[Image.Image] = None

        self.setWindowTitle("Crop Image - Draw Rectangle")
        self.resize(800, 600)

        # Apply parent's theme/style
        if parent:
            self.setStyleSheet(parent.styleSheet())
            self.setPalette(parent.palette())

        layout = QVBoxLayout(self)

        # Graphics View for Cropping
        self.scene = QGraphicsScene(self)
        self.view = QGraphicsView(self.scene)
        self.view.setRenderHint(QPainter.Antialiasing)
        self.view.setRenderHint(QPainter.SmoothPixmapTransform)
        layout.addWidget(self.view)

        # Display the image
        self.qimage = pil_image_to_qimage(self.image)
        if self.qimage is None:
             logger.error("Failed to convert image for crop dialog.")
             QTimer.singleShot(0, self.reject) # Reject immediately if image fails
             return
        self.pixmap_item = QGraphicsPixmapItem(QPixmap.fromImage(self.qimage))
        self.scene.addItem(self.pixmap_item)
        self.view.fitInView(self.pixmap_item, Qt.KeepAspectRatio)

        # Cropping Rectangle Item
        self.crop_rect_item = QGraphicsRectItem(self.pixmap_item) # Child of pixmap item
        pen = QPen(Qt.red, 2, Qt.DashLine)
        pen.setCosmetic(True) # Keep thickness consistent on zoom
        self.crop_rect_item.setPen(pen)
        self.crop_rect_item.setBrush(QColor(255, 0, 0, 40)) # Semi-transparent fill
        self.crop_rect_item.hide()

        # Mouse Handling for Drawing Crop Rect
        self._start_pos_scene: Optional[QPointF] = None
        self.view.mousePressEvent = self._mousePress
        self.view.mouseMoveEvent = self._mouseMove
        self.view.mouseReleaseEvent = self._mouseRelease

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.button(QDialogButtonBox.Ok).setText("Apply Crop")
        button_box.accepted.connect(self.apply_crop)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)


    def _mousePress(self, event: QtGui.QMouseEvent):
        if event.button() == Qt.LeftButton:
            self._start_pos_scene = self.view.mapToScene(event.pos())
            # Ensure start pos is within image bounds
            self._start_pos_scene = self._clamp_to_image(self._start_pos_scene)

            self.crop_rect_item.setRect(QRectF(self._start_pos_scene, self._start_pos_scene))
            self.crop_rect_item.show()
        else:
            # Use super() for QGraphicsView's event handling
            super(QGraphicsView, self.view).mousePressEvent(event)


    def _mouseMove(self, event: QtGui.QMouseEvent):
        if self._start_pos_scene:
            current_pos = self.view.mapToScene(event.pos())
            current_pos = self._clamp_to_image(current_pos) # Clamp move position too
            self.crop_rect_item.setRect(QRectF(self._start_pos_scene, current_pos).normalized())
        else:
            # Use super() for QGraphicsView's event handling
            super(QGraphicsView, self.view).mouseMoveEvent(event)


    def _mouseRelease(self, event: QtGui.QMouseEvent):
        if event.button() == Qt.LeftButton and self._start_pos_scene:
            final_rect = self.crop_rect_item.rect()
            self._start_pos_scene = None # End drawing
            # Store the final rect relative to the scene (pixmap item)
            self.crop_rect_scene = self.crop_rect_item.mapRectToParent(final_rect)
            logger.debug(f"Crop rectangle drawn (scene coords): {self.crop_rect_scene}")
        else:
             # Use super() for QGraphicsView's event handling
             super(QGraphicsView, self.view).mouseReleaseEvent(event)


    def _clamp_to_image(self, scene_pos: QPointF) -> QPointF:
        """Clamps scene coordinates to the boundaries of the pixmap item."""
        img_bounds = self.pixmap_item.boundingRect() # Coords relative to pixmap item's origin (0,0)
        clamped_x = max(img_bounds.left(), min(scene_pos.x(), img_bounds.right()))
        clamped_y = max(img_bounds.top(), min(scene_pos.y(), img_bounds.bottom()))
        return QPointF(clamped_x, clamped_y)

    def apply_crop(self):
        """Applies the selected crop rectangle to the original PIL image."""
        if self.crop_rect_scene is None or not self.crop_rect_scene.isValid() or \
           self.crop_rect_scene.width() < 1 or self.crop_rect_scene.height() < 1:
            QMessageBox.warning(self, "No Crop Area", "Please draw a valid crop rectangle first.")
            return

        try:
            # Convert scene rectangle (relative to pixmap origin) to original image pixel coords
            display_width = self.pixmap_item.pixmap().width()
            display_height = self.pixmap_item.pixmap().height()
            original_width = self.image.width
            original_height = self.image.height

            if display_width == 0 or display_height == 0: raise ValueError("Pixmap has zero dimension")

            ratio_x = original_width / display_width
            ratio_y = original_height / display_height

            # Coords are relative to pixmap top-left (0,0)
            orig_x0 = int(self.crop_rect_scene.left() * ratio_x)
            orig_y0 = int(self.crop_rect_scene.top() * ratio_y)
            orig_x1 = int(self.crop_rect_scene.right() * ratio_x)
            orig_y1 = int(self.crop_rect_scene.bottom() * ratio_y)

            # Clamp to original image boundaries just in case
            orig_x0 = max(0, orig_x0)
            orig_y0 = max(0, orig_y0)
            orig_x1 = min(original_width, orig_x1)
            orig_y1 = min(original_height, orig_y1)

            if (orig_x1 - orig_x0) < 1 or (orig_y1 - orig_y0) < 1:
                raise ValueError("Calculated crop area is too small.")

            self.cropped_image = self.image.crop((orig_x0, orig_y0, orig_x1, orig_y1))
            logger.info(f"Image cropped to: {(orig_x0, orig_y0, orig_x1, orig_y1)}")
            self.accept() # Close dialog with accept code

        except Exception as e:
            logger.exception(f"Error applying crop: {e}")
            QMessageBox.critical(self, "Crop Error", f"Could not apply crop: {e}")
            self.cropped_image = None # Ensure no invalid image is returned

    def get_cropped_image(self) -> Optional[Image.Image]:
        return self.cropped_image

# --- Function to show crop dialog ---
def show_crop_dialog(image: Image.Image, parent=None) -> Optional[Image.Image]:
    """Shows the crop dialog and returns the cropped PIL image if successful."""
    dialog = CropDialog(image, parent)
    result = dialog.exec() # Show modally
    if result == QDialog.Accepted:
        return dialog.get_cropped_image()
    return None


# --- About Dialog ---

class AboutDialog(QDialog):
    """Simple About dialog."""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("About VisionLab Ai")

        # Apply parent's theme/style
        if parent:
            self.setStyleSheet(parent.styleSheet())
            self.setPalette(parent.palette())

        layout = QVBoxLayout(self)

        title_label = QLabel("VisionLab Ai")
        title_font = title_label.font()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        version_label = QLabel("Version 4.0") # Update as needed
        version_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(version_label)

        description_label = QLabel(
            "VisionLab Ai is a tool for segmentation and analysis of geological images, "
            "specializing in grain analysis."
        )
        description_label.setWordWrap(True)
        description_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(description_label)

        layout.addSpacing(15)

        # Optional: Add more info like credits, links, etc.
        credits_label = QLabel("Developed by: [Your Name/Organization]")
        credits_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(credits_label)

        layout.addSpacing(15)

        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

        self.setMinimumWidth(350)


# --- Patch Configuration Dialog ---

class PatchConfigDialog(QDialog):
    """Dialog to configure patch segmentation parameters."""
    def __init__(self, image_width, image_height, parent=None):
        super().__init__(parent)
        self.image_width = image_width
        self.image_height = image_height
        self.patch_config = None # To store results

        self.setWindowTitle("Patch Segmentation Configuration")

        # Apply parent's theme/style
        if parent:
            self.setStyleSheet(parent.styleSheet())
            self.setPalette(parent.palette())

        layout = QVBoxLayout(self)

        # --- Patching Mode Selection ---
        mode_groupbox = QGroupBox("Patching Mode")
        mode_layout = QVBoxLayout()
        mode_groupbox.setLayout(mode_layout)

        self.no_patching_radio = QRadioButton("No Patching (Single Patch)")
        self.no_patching_radio.setToolTip("Best for small images or few grains. Processes the whole image at once.")
        mode_layout.addWidget(self.no_patching_radio)

        self.custom_patching_radio = QRadioButton("Custom Patching")
        self.custom_patching_radio.setToolTip("Divide the image into smaller patches for processing.")
        mode_layout.addWidget(self.custom_patching_radio)

        layout.addWidget(mode_groupbox)

        # --- Patch Parameters ---
        self.params_groupbox = QGroupBox("Patch Parameters") # Group parameters
        grid_layout = QGridLayout()
        self.params_groupbox.setLayout(grid_layout)
        layout.addWidget(self.params_groupbox)

        # Rows Input
        self.rows_label = QLabel("Number of Rows:")
        grid_layout.addWidget(self.rows_label, 0, 0)
        self.rows_spinbox = QSpinBox()
        self.rows_spinbox.setRange(1, 100) # Adjust max as needed
        self.rows_spinbox.setValue(3) # Default
        self.rows_spinbox.valueChanged.connect(self.update_info)
        grid_layout.addWidget(self.rows_spinbox, 0, 1)

        # Columns Input
        self.cols_label = QLabel("Number of Columns:")
        grid_layout.addWidget(self.cols_label, 1, 0)
        self.cols_spinbox = QSpinBox()
        self.cols_spinbox.setRange(1, 100)
        self.cols_spinbox.setValue(3) # Default
        self.cols_spinbox.valueChanged.connect(self.update_info)
        grid_layout.addWidget(self.cols_spinbox, 1, 1)

        # Overlap Input (Percentage)
        self.overlap_label = QLabel("Overlap (%):")
        grid_layout.addWidget(self.overlap_label, 2, 0)
        self.overlap_spinbox = QSpinBox()
        self.overlap_spinbox.setRange(0, 90) # Percentage overlap
        self.overlap_spinbox.setValue(15) # Default 15%
        self.overlap_spinbox.setSuffix(" %")
        self.overlap_spinbox.valueChanged.connect(self.update_info)
        grid_layout.addWidget(self.overlap_spinbox, 2, 1)

        # --- Advanced Settings ---
        advanced_groupbox = QGroupBox("Advanced Artifact Control")
        advanced_layout = QGridLayout()
        advanced_groupbox.setLayout(advanced_layout)
        layout.addWidget(advanced_groupbox)

        # Artifact Sensitivity
        advanced_layout.addWidget(QLabel("Artifact Sensitivity:"), 0, 0)
        self.artifact_sensitivity_slider = QSlider(Qt.Horizontal)
        self.artifact_sensitivity_slider.setRange(0, 100)  # 0-100 for 0.0-1.0
        self.artifact_sensitivity_slider.setValue(50)  # Default 0.5
        self.artifact_sensitivity_slider.setTickInterval(10)
        self.artifact_sensitivity_slider.setTickPosition(QSlider.TicksBelow)
        self.artifact_sensitivity_slider.setToolTip("Higher values remove more rectangular artifacts (0.0 = keep all, 1.0 = aggressive filtering)")
        advanced_layout.addWidget(self.artifact_sensitivity_slider, 0, 1)
        self.artifact_sensitivity_label = QLabel("0.50")
        self.artifact_sensitivity_label.setMinimumWidth(40)
        advanced_layout.addWidget(self.artifact_sensitivity_label, 0, 2)

        # Duplicate Sensitivity
        advanced_layout.addWidget(QLabel("Duplicate Sensitivity:"), 1, 0)
        self.duplicate_sensitivity_slider = QSlider(Qt.Horizontal)
        self.duplicate_sensitivity_slider.setRange(0, 100)  # 0-100 for 0.0-1.0
        self.duplicate_sensitivity_slider.setValue(70)  # Default 0.7
        self.duplicate_sensitivity_slider.setTickInterval(10)
        self.duplicate_sensitivity_slider.setTickPosition(QSlider.TicksBelow)
        self.duplicate_sensitivity_slider.setToolTip("Higher values remove more duplicate grains at patch boundaries (0.0 = keep all, 1.0 = aggressive removal)")
        advanced_layout.addWidget(self.duplicate_sensitivity_slider, 1, 1)
        self.duplicate_sensitivity_label = QLabel("0.70")
        self.duplicate_sensitivity_label.setMinimumWidth(40)
        advanced_layout.addWidget(self.duplicate_sensitivity_label, 1, 2)

        # Connect slider signals
        self.artifact_sensitivity_slider.valueChanged.connect(self._update_sensitivity_labels)
        self.duplicate_sensitivity_slider.valueChanged.connect(self._update_sensitivity_labels)

        # Info Label
        self.info_label = QLabel("Calculating patch info...")
        self.info_label.setWordWrap(True)
        layout.addWidget(self.info_label)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_config)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Connect radio buttons AFTER spinboxes are created
        self.no_patching_radio.toggled.connect(self._update_patching_mode)
        self.custom_patching_radio.toggled.connect(self._update_patching_mode)

        # Set initial state (default to custom)
        self.custom_patching_radio.setChecked(True)
        self._update_patching_mode() # Trigger initial state update


    def _update_patching_mode(self):
        """Enable/disable controls based on selected patching mode."""
        is_custom = self.custom_patching_radio.isChecked()

        self.params_groupbox.setEnabled(is_custom) # Enable/disable the whole group

        if not is_custom:
            # Store current values before changing? Not strictly necessary here.
            self.rows_spinbox.setValue(1)
            self.cols_spinbox.setValue(1)
            # Keep overlap enabled, it might still be relevant conceptually even if not used for 1x1 grid

            # Add a note about artifact handling being disabled in single patch mode
            self.info_label.setText(
                "Mode: No Patching (Whole Image)\n"
                "Note: Intelligent artifact handling will be automatically disabled in single patch mode."
            )
        else:
            # Update info normally
            self.update_info() # Refresh info label

    def _update_sensitivity_labels(self):
        """Updates the labels next to sensitivity sliders."""
        artifact_val = self.artifact_sensitivity_slider.value() / 100.0
        duplicate_val = self.duplicate_sensitivity_slider.value() / 100.0
        self.artifact_sensitivity_label.setText(f"{artifact_val:.2f}")
        self.duplicate_sensitivity_label.setText(f"{duplicate_val:.2f}")


    def update_info(self):
        """Updates the label showing calculated patch size."""
        try:
            rows = self.rows_spinbox.value()
            cols = self.cols_spinbox.value()
            overlap_percent = self.overlap_spinbox.value() / 100.0

            if rows <= 0 or cols <= 0:
                self.info_label.setText("Invalid rows/columns.")
                return

            # Calculate base patch size (without overlap)
            base_patch_w = self.image_width / cols
            base_patch_h = self.image_height / rows

            # Calculate overlap in pixels
            overlap_w_px = base_patch_w * overlap_percent
            overlap_h_px = base_patch_h * overlap_percent

            # Calculate actual patch size including overlap (approximate)
            patch_w = int(base_patch_w + overlap_w_px)
            patch_h = int(base_patch_h + overlap_h_px)
            # Ensure patch size isn't larger than the image itself
            patch_w = min(patch_w, self.image_width)
            patch_h = min(patch_h, self.image_height)

            mode_text = "Mode: Custom Patching" if self.custom_patching_radio.isChecked() else "Mode: No Patching (Whole Image)"

            self.info_label.setText(
                f"{mode_text}\n"
                f"Image: {self.image_width}x{self.image_height} px\n"
                f"Grid: {rows} rows x {cols} columns\n"
                f"Overlap: {self.overlap_spinbox.value()} %\n"
                f"Approx. Patch Size (for processing): ~{patch_w}x{patch_h} px"
            )
        except Exception as e:
            self.info_label.setText(f"Error calculating info: {e}")
            logger.error(f"Error in patch info update: {e}")


    def accept_config(self):
        if self.no_patching_radio.isChecked():
            rows = 1
            cols = 1
        else: # Custom patching
            rows = self.rows_spinbox.value()
            cols = self.cols_spinbox.value()

        overlap = self.overlap_spinbox.value() / 100.0 # Store as fraction
        artifact_sensitivity = self.artifact_sensitivity_slider.value() / 100.0
        duplicate_sensitivity = self.duplicate_sensitivity_slider.value() / 100.0

        if rows < 1 or cols < 1:
            QMessageBox.warning(self, "Invalid Input", "Rows and columns must be at least 1.")
            return

        self.patch_config = {
            'rows': rows,
            'cols': cols,
            'overlap': overlap,
            'artifact_sensitivity': artifact_sensitivity,
            'duplicate_sensitivity': duplicate_sensitivity
        }
        self.accept()

    def get_config(self) -> Optional[dict]:
        return self.patch_config

# --- Function to show patch config dialog ---
def show_patch_config_dialog(image_width, image_height, parent=None) -> Optional[dict]:
    dialog = PatchConfigDialog(image_width, image_height, parent)
    if dialog.exec() == QDialog.Accepted:
        return dialog.get_config()
    return None

# --- Function to show about dialog ---
def show_about_dialog(parent=None):
    """Shows the About dialog."""
    dialog = AboutDialog(parent)
    dialog.exec() # Show modally