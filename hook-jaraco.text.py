from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# Collect all submodules of jaraco.text and other potentially needed jaraco packages
hiddenimports = collect_submodules('jaraco.text')
hiddenimports += collect_submodules('jaraco.functools')
hiddenimports += collect_submodules('jaraco.collections')
hiddenimports += collect_submodules('jaraco.itertools')
hiddenimports += collect_submodules('jaraco.classes')
hiddenimports += collect_submodules('jaraco.context')
hiddenimports += collect_submodules('jaraco.path')
hiddenimports += collect_submodules('jaraco.logging') # Add if logging features are used
hiddenimports += collect_submodules('jaraco.stream') # Add if stream features are used
hiddenimports += ['pkg_resources'] # Ensure pkg_resources is included

# Collect data files associated with jaraco packages if any
# datas = collect_data_files('jaraco.text', include_py_files=True)
# datas += collect_data_files('jaraco.functools', include_py_files=True)
# datas += collect_data_files('jaraco', include_py_files=True) # To get top-level jaraco files

# For namespace packages, sometimes it's better to explicitly list them
# or ensure the __init__.py files are present and collected.

# If jaraco.text relies on specific data files, they should be added here.
# For example:
# datas = [('path/to/jaraco/text/datafile.dat', 'jaraco/text/data')]

# For now, we'll focus on hidden imports as that's the most common need for jaraco.
# If data files are also missing, the datas variable can be populated and returned.
datas = []