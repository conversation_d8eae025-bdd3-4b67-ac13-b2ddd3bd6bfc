"""
Patch-based inference module for large images.
This module provides functions to split large images into patches,
run inference on each patch, and combine the results.
"""

import logging
import numpy as np
import cv2
import torch
import time
import os
import sys
from typing import Dict, List, Tuple, Any, Optional, Union

# Don't import Detectron2 modules at the top level
# They will be imported when needed in the functions

logger = logging.getLogger(__name__)

def split_image_into_patches(
    image: np.ndarray,
    patch_size: Tuple[int, int],
    overlap: int = 0
) -> Tuple[List[np.ndarray], List[Tuple[int, int]]]:
    """
    Split an image into patches of specified size with optional overlap.

    Args:
        image: Input image as numpy array (H, W, C)
        patch_size: Tuple of (height, width) for each patch
        overlap: Number of pixels to overlap between patches

    Returns:
        Tuple containing:
        - List of image patches as numpy arrays
        - List of (y, x) coordinates for the top-left corner of each patch
    """
    height, width = image.shape[:2]
    patch_height, patch_width = patch_size

    # Ensure patch size is not larger than image
    patch_height = min(patch_height, height)
    patch_width = min(patch_width, width)

    # Calculate step size (accounting for overlap)
    step_y = max(1, patch_height - overlap)
    step_x = max(1, patch_width - overlap)

    patches = []
    coordinates = []

    # Generate patches
    for y in range(0, height, step_y):
        if y + patch_height > height:
            y = max(0, height - patch_height)  # Adjust to fit within image

        for x in range(0, width, step_x):
            if x + patch_width > width:
                x = max(0, width - patch_width)  # Adjust to fit within image

            # Extract patch
            patch = image[y:y+patch_height, x:x+patch_width].copy()
            patches.append(patch)
            coordinates.append((y, x))

            # If we've reached the right edge, break to avoid duplicates
            if x + patch_width >= width:
                break

        # If we've reached the bottom edge, break to avoid duplicates
        if y + patch_height >= height:
            break

    return patches, coordinates

def run_inference_on_patches(
    predictor,
    patches: List[np.ndarray],
    coordinates: List[Tuple[int, int]],
    original_size: Tuple[int, int],
    model_type: str,
    metadata,
    progress_callback=None,
    refine_masks=False,
    sam_handler=None
) -> Tuple[Dict[str, Any], List[Dict[str, Any]], float]:
    """
    Run inference on image patches and combine results.

    Args:
        predictor: Detectron2 DefaultPredictor instance
        patches: List of image patches
        coordinates: List of (y, x) coordinates for each patch
        original_size: (height, width) of the original image
        model_type: "Faster R-CNN" or "Mask R-CNN"
        metadata: Detectron2 metadata for visualization
        progress_callback: Optional callback function to report progress
        refine_masks: Whether to refine masks for Faster R-CNN models using SAM
        sam_handler: SAM segmentation handler for mask refinement

    Returns:
        Tuple containing:
        - Combined results dictionary
        - List of per-patch results
        - Total inference time
    """
    # Import Detectron2 modules here to ensure they're available
    try:
        from detectron2.structures import Instances, Boxes
        from detectron2.utils.visualizer import Visualizer
        from detectron2.data import MetadataCatalog
    except ImportError:
        # Try to import from local path
        detectron2_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'detectron2')
        if os.path.exists(detectron2_path):
            sys.path.append(os.path.dirname(detectron2_path))
            from detectron2.structures import Instances, Boxes
            from detectron2.utils.visualizer import Visualizer
            from detectron2.data import MetadataCatalog
        else:
            raise ImportError("Detectron2 not found in local path")
    original_height, original_width = original_size
    patch_results = []
    all_boxes = []
    all_scores = []
    all_classes = []
    all_masks = [] if model_type == "Mask R-CNN" or (model_type == "Faster R-CNN" and refine_masks) else None

    # Initialize refinement time tracking
    refinement_times = []

    # Create a blank canvas for visualization
    if model_type == "Faster R-CNN":
        # For Faster R-CNN, we'll draw on an RGB canvas
        visualization_canvas = np.zeros((original_height, original_width, 3), dtype=np.uint8)
    else:
        # For Mask R-CNN, we'll draw on an RGB canvas
        visualization_canvas = np.zeros((original_height, original_width, 3), dtype=np.uint8)

    total_start_time = time.time()
    total_instances = 0

    # Process each patch
    for i, (patch, (y, x)) in enumerate(zip(patches, coordinates)):
        patch_start_time = time.time()

        # Report progress if callback provided
        if progress_callback:
            progress_callback(i + 1, len(patches), f"Processing patch {i+1}/{len(patches)}")

        # Run inference on patch
        outputs = predictor(patch)
        instances = outputs["instances"].to("cpu")

        # Get patch results
        patch_boxes = instances.pred_boxes.tensor.numpy() if len(instances) > 0 else np.zeros((0, 4))
        patch_scores = instances.scores.numpy() if len(instances) > 0 else np.zeros(0)
        patch_classes = instances.pred_classes.numpy() if len(instances) > 0 else np.zeros(0, dtype=np.int64)

        # Adjust bounding box coordinates to original image space
        if len(patch_boxes) > 0:
            # Add offset to convert patch coordinates to image coordinates
            adjusted_boxes = patch_boxes.copy()
            adjusted_boxes[:, 0] += x  # x1
            adjusted_boxes[:, 2] += x  # x2
            adjusted_boxes[:, 1] += y  # y1
            adjusted_boxes[:, 3] += y  # y3

            # Clip to image boundaries
            adjusted_boxes[:, 0] = np.clip(adjusted_boxes[:, 0], 0, original_width - 1)
            adjusted_boxes[:, 2] = np.clip(adjusted_boxes[:, 2], 0, original_width - 1)
            adjusted_boxes[:, 1] = np.clip(adjusted_boxes[:, 1], 0, original_height - 1)
            adjusted_boxes[:, 3] = np.clip(adjusted_boxes[:, 3], 0, original_height - 1)

            all_boxes.append(adjusted_boxes)
            all_scores.append(patch_scores)
            all_classes.append(patch_classes)

            # For Mask R-CNN, adjust masks
            if model_type == "Mask R-CNN" and hasattr(instances, "pred_masks"):
                patch_masks = instances.pred_masks.numpy()

                # Create full-sized masks and place patch masks at the correct position
                adjusted_masks = []
                for mask in patch_masks:
                    full_mask = np.zeros((original_height, original_width), dtype=bool)
                    h, w = mask.shape
                    # Place the patch mask in the correct position
                    full_mask[y:y+h, x:x+w] = mask
                    adjusted_masks.append(full_mask)

                if all_masks is not None and adjusted_masks:
                    all_masks.extend(adjusted_masks)

            # For Faster R-CNN with mask refinement, generate masks from boxes using SAM
            elif model_type == "Faster R-CNN" and refine_masks and len(adjusted_boxes) > 0 and sam_handler is not None:
                # Import SAM handler if needed
                try:
                    from src.segmentation.sam_segmentation_handler import SAMSegmentationHandler
                except ImportError:
                    # Try to import from local path
                    import sys
                    import os
                    sam_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'segmentation')
                    if os.path.exists(sam_path):
                        sys.path.append(os.path.dirname(sam_path))
                        from src.segmentation.sam_segmentation_handler import SAMSegmentationHandler
                    else:
                        logger.warning("SAM segmentation handler not found, skipping mask refinement")

                # Set the current patch image for SAM
                if sam_handler.set_image(patch):
                    # Process each box
                    for j, box in enumerate(patch_boxes):
                        # Start timing for this mask refinement
                        refinement_start_time = time.time()

                        # Predict mask using SAM
                        masks, scores, _ = sam_handler.predict_from_box(box)

                        # Calculate refinement time
                        refinement_time = time.time() - refinement_start_time
                        refinement_times.append(refinement_time)

                        if masks is not None and len(masks) > 0:
                            # Get the best mask
                            best_mask_idx = np.argmax(scores)
                            refined_mask = masks[best_mask_idx]

                            # Create full-sized mask and place the refined mask at the correct position
                            full_mask = np.zeros((original_height, original_width), dtype=bool)
                            h, w = refined_mask.shape
                            # Place the refined mask in the correct position
                            full_mask[y:y+h, x:x+w] = refined_mask

                            if all_masks is not None:
                                all_masks.append(full_mask)

        # Create visualization for this patch
        visualizer = Visualizer(patch, metadata=metadata, scale=1.0)
        vis_output = visualizer.draw_instance_predictions(instances)
        patch_vis = vis_output.get_image()

        # Place the visualization on the canvas
        h, w = patch.shape[:2]
        visualization_canvas[y:y+h, x:x+w] = patch_vis

        # Store patch results
        patch_inference_time = time.time() - patch_start_time
        patch_result = {
            "boxes": patch_boxes,
            "adjusted_boxes": adjusted_boxes if len(patch_boxes) > 0 else np.zeros((0, 4)),
            "scores": patch_scores,
            "classes": patch_classes,
            "visualization": patch_vis,
            "inference_time": patch_inference_time,
            "coordinates": (y, x),
            "size": (h, w)
        }

        if model_type == "Mask R-CNN" and hasattr(instances, "pred_masks"):
            patch_result["masks"] = instances.pred_masks.numpy()

        patch_results.append(patch_result)
        total_instances += len(instances)

        logger.info(f"Patch {i+1}/{len(patches)} at ({x},{y}): {len(instances)} instances, {patch_inference_time:.3f}s")

    # Combine results
    total_inference_time = time.time() - total_start_time

    # Concatenate all detections
    if all_boxes and len(all_boxes) > 0:
        combined_boxes = np.vstack(all_boxes) if all_boxes else np.zeros((0, 4))
        combined_scores = np.concatenate(all_scores) if all_scores else np.zeros(0)
        combined_classes = np.concatenate(all_classes) if all_classes else np.zeros(0, dtype=np.int64)
    else:
        combined_boxes = np.zeros((0, 4))
        combined_scores = np.zeros(0)
        combined_classes = np.zeros(0, dtype=np.int64)

    # Create combined results dictionary
    combined_results = {
        "boxes": combined_boxes,
        "scores": combined_scores,
        "classes": combined_classes,
        "visualization": visualization_canvas,
        "inference_time": total_inference_time,
        "annotations": []
    }

    # Add refinement times if available
    if refinement_times:
        combined_results["refinement_times"] = {
            "total_time": sum(refinement_times),
            "average_time": sum(refinement_times) / len(refinement_times) if refinement_times else 0,
            "count": len(refinement_times)
        }

    # Add masks for Mask R-CNN or Faster R-CNN with refinement
    if (model_type == "Mask R-CNN" or (model_type == "Faster R-CNN" and refine_masks)) and all_masks is not None:
        combined_results["masks"] = np.array(all_masks) if all_masks else np.zeros((0, original_height, original_width), dtype=bool)

    # Convert to annotations format
    if model_type == "Faster R-CNN":
        for j in range(len(combined_boxes)):
            box = combined_boxes[j]
            score = combined_scores[j]
            class_id = int(combined_classes[j]) + 1  # Convert from 0-indexed to 1-indexed

            # Create annotation
            if refine_masks and "masks" in combined_results and j < len(combined_results["masks"]):
                # If mask refinement was applied, use the mask annotation
                mask = combined_results["masks"][j]
                annotation = {
                    'type': 'mask',
                    'mask': mask,
                    'box': box,  # Include the original box for reference
                    'class_id': class_id,
                    'score': float(score),
                    'refined': True  # Mark as refined
                }
            else:
                # Otherwise use rectangle annotation
                annotation = {
                    'type': 'rectangle',
                    'points': [(box[0], box[1]), (box[2], box[3])],
                    'class_id': class_id,
                    'score': float(score)
                }

            combined_results["annotations"].append(annotation)
    elif model_type == "Mask R-CNN" and "masks" in combined_results:
        masks = combined_results["masks"]
        for j in range(len(masks)):
            mask = masks[j]
            score = combined_scores[j]
            class_id = int(combined_classes[j]) + 1  # Convert from 0-indexed to 1-indexed

            # Create mask annotation
            annotation = {
                'type': 'mask',
                'mask': mask,
                'class_id': class_id,
                'score': float(score)
            }

            combined_results["annotations"].append(annotation)

    logger.info(f"Patch-based inference complete: {total_instances} total instances across {len(patches)} patches, {total_inference_time:.3f}s")

    return combined_results, patch_results, total_inference_time
