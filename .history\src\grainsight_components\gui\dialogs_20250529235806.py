# gui/dialogs.py

import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, 
    QDoubleSpinBox, QPushButton, QFormLayout, QGroupBox,
    QDialogButtonBox, QMessageBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

logger = logging.getLogger(__name__)

class PatchConfigurationDialog(QDialog):
    """Dialog for configuring patch-based segmentation parameters."""
    
    def __init__(self, image_width: int, image_height: int, parent=None):
        super().__init__(parent)
        self.image_width = image_width
        self.image_height = image_height
        self.patch_config = None
        
        self.setWindowTitle("Patch Configuration")
        self.setModal(True)
        self.resize(400, 300)
        
        self.setup_ui()
        self.calculate_recommended_values()
        
    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        
        # Image info group
        info_group = QGroupBox("Image Information")
        info_layout = QFormLayout(info_group)
        
        info_layout.addRow("Width:", QLabel(f"{self.image_width} pixels"))
        info_layout.addRow("Height:", QLabel(f"{self.image_height} pixels"))
        
        layout.addWidget(info_group)
        
        # Patch configuration group
        config_group = QGroupBox("Patch Configuration")
        config_layout = QFormLayout(config_group)
        
        # Rows spinbox
        self.rows_spinbox = QSpinBox()
        self.rows_spinbox.setRange(1, 20)
        self.rows_spinbox.setValue(3)
        self.rows_spinbox.valueChanged.connect(self.update_preview)
        config_layout.addRow("Rows:", self.rows_spinbox)
        
        # Columns spinbox
        self.cols_spinbox = QSpinBox()
        self.cols_spinbox.setRange(1, 20)
        self.cols_spinbox.setValue(3)
        self.cols_spinbox.valueChanged.connect(self.update_preview)
        config_layout.addRow("Columns:", self.cols_spinbox)
        
        # Overlap spinbox
        self.overlap_spinbox = QDoubleSpinBox()
        self.overlap_spinbox.setRange(0.0, 0.9)
        self.overlap_spinbox.setSingleStep(0.1)
        self.overlap_spinbox.setValue(0.2)
        self.overlap_spinbox.setDecimals(2)
        self.overlap_spinbox.setSuffix(" (20%)")
        self.overlap_spinbox.valueChanged.connect(self.update_overlap_display)
        config_layout.addRow("Overlap:", self.overlap_spinbox)
        
        layout.addWidget(config_group)
        
        # Preview group
        preview_group = QGroupBox("Preview")
        preview_layout = QFormLayout(preview_group)
        
        self.patch_size_label = QLabel()
        self.total_patches_label = QLabel()
        
        preview_layout.addRow("Patch Size:", self.patch_size_label)
        preview_layout.addRow("Total Patches:", self.total_patches_label)
        
        layout.addWidget(preview_group)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # Add recommended button
        recommended_btn = QPushButton("Use Recommended")
        recommended_btn.clicked.connect(self.use_recommended)
        button_box.addButton(recommended_btn, QDialogButtonBox.ActionRole)
        
        layout.addWidget(button_box)
        
    def calculate_recommended_values(self):
        """Calculate recommended patch configuration based on image size."""
        # Aim for patches around 512-1024 pixels
        target_patch_size = 768
        
        self.recommended_rows = max(1, min(10, self.image_height // target_patch_size))
        self.recommended_cols = max(1, min(10, self.image_width // target_patch_size))
        self.recommended_overlap = 0.2
        
        # Ensure at least 2x2 for large images
        if self.image_width > 1500 or self.image_height > 1500:
            self.recommended_rows = max(2, self.recommended_rows)
            self.recommended_cols = max(2, self.recommended_cols)
            
    def use_recommended(self):
        """Set recommended values."""
        self.rows_spinbox.setValue(self.recommended_rows)
        self.cols_spinbox.setValue(self.recommended_cols)
        self.overlap_spinbox.setValue(self.recommended_overlap)
        
    def update_overlap_display(self, value):
        """Update overlap percentage display."""
        percentage = int(value * 100)
        self.overlap_spinbox.setSuffix(f" ({percentage}%)")
        self.update_preview()
        
    def update_preview(self):
        """Update the preview information."""
        rows = self.rows_spinbox.value()
        cols = self.cols_spinbox.value()
        overlap = self.overlap_spinbox.value()
        
        # Calculate patch dimensions
        patch_height = self.image_height // rows
        patch_width = self.image_width // cols
        
        # Calculate effective patch size with overlap
        effective_height = int(patch_height * (1 + overlap))
        effective_width = int(patch_width * (1 + overlap))
        
        total_patches = rows * cols
        
        self.patch_size_label.setText(f"{effective_width} × {effective_height} pixels")
        self.total_patches_label.setText(f"{total_patches} patches")
        
    def accept(self):
        """Accept the dialog and store configuration."""
        rows = self.rows_spinbox.value()
        cols = self.cols_spinbox.value()
        overlap = self.overlap_spinbox.value()
        
        # Validate configuration
        if rows < 1 or cols < 1:
            QMessageBox.warning(self, "Invalid Configuration", 
                              "Rows and columns must be at least 1.")
            return
            
        if overlap < 0 or overlap >= 1:
            QMessageBox.warning(self, "Invalid Configuration", 
                              "Overlap must be between 0 and 1.")
            return
            
        self.patch_config = {
            'rows': rows,
            'cols': cols,
            'overlap': overlap
        }
        
        logger.info(f"Patch configuration accepted: {self.patch_config}")
        super().accept()
        
    def get_config(self):
        """Get the patch configuration."""
        return self.patch_config


def show_patch_config_dialog(image_width: int, image_height: int, parent=None):
    """Show patch configuration dialog and return configuration.
    
    Args:
        image_width: Width of the image in pixels
        image_height: Height of the image in pixels
        parent: Parent widget
        
    Returns:
        dict: Patch configuration with 'rows', 'cols', 'overlap' keys,
              or None if canceled
    """
    try:
        dialog = PatchConfigurationDialog(image_width, image_height, parent)
        
        if dialog.exec_() == QDialog.Accepted:
            return dialog.get_config()
        else:
            logger.info("Patch configuration dialog canceled")
            return None
            
    except Exception as e:
        logger.error(f"Error showing patch configuration dialog: {e}")
        if parent:
            QMessageBox.critical(parent, "Error", 
                               f"Failed to show patch configuration dialog: {e}")
        return None