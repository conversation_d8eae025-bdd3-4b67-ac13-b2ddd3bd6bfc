#!/usr/bin/env python3
"""
GrainSight Installer Builder
Creates a directory-based executable distribution for easy installation by non-technical users.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import zipfile

def check_requirements():
    """Check if all required tools and files are available."""
    print("[INFO] Checking requirements...")

    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print(f"[OK] PyInstaller {PyInstaller.__version__} found")
    except ImportError:
        print("[WARN] PyInstaller not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("[OK] PyInstaller installed")

    # Check if main script exists
    if not os.path.exists("grainsight.py"):
        print("[ERROR] Main script 'grainsight.py' not found!")
        return False
    print("[OK] Main script found")

    return True

def create_spec_file():
    """Create PyInstaller spec file for directory-based distribution."""
    print("[INFO] Creating PyInstaller spec file...")

    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files and submodules
datas = []
hiddenimports = []

# Add source code as data files
datas += [('src', 'src')]

# Add configuration files
if os.path.exists('theme_config.json'):
    datas += [('theme_config.json', '.')]

# Add model files if they exist
if os.path.exists('src/grainsight_components/models'):
    datas += [('src/grainsight_components/models', 'src/grainsight_components/models')]

# Add icon files and assets
if os.path.exists('src/grainsight_components/gui/assets'):
    datas += [('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets')]

# Collect PyTorch data files
try:
    datas += collect_data_files('torch')
    datas += collect_data_files('torchvision')
except:
    pass

# Collect Ultralytics data files
try:
    datas += collect_data_files('ultralytics')
except:
    pass

# Collect timm data files
try:
    datas += collect_data_files('timm')
except:
    pass

# Collect PySide6 data files
try:
    datas += collect_data_files('PySide6')
except:
    pass

# Hidden imports for modules that might not be detected
hiddenimports += [
    # Core Python modules
    'inspect',
    'importlib',
    'importlib.resources',
    'json',
    'logging',
    'threading',
    'time',
    'datetime',
    'locale',
    'math',
    'sys',
    'os',
    
    # Scientific computing
    'numpy',
    'pandas',
    'cv2',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    
    # PyTorch and related
    'torch',
    'torchvision',
    'torch.nn',
    'torch.optim',
    'torch.utils',
    'torch.utils.data',
    'timm',
    'timm.models',
    
    # Ultralytics YOLO
    'ultralytics',
    'ultralytics.models',
    'ultralytics.models.fastsam',
    'ultralytics.models.sam',
    
    # PySide6 GUI
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtOpenGL',
    'PySide6.QtOpenGLWidgets',
    
    # Matplotlib
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.figure',
    
    # Application modules
    'src.grainsight_components',
    'src.grainsight_components.gui',
    'src.grainsight_components.gui.main_window',
    'src.grainsight_components.gui.utils',
    'src.grainsight_components.config',
    'src.grainsight_components.analysis',
    'src.grainsight_components.models',
]

# Collect all submodules from our source
try:
    hiddenimports += collect_submodules('src.grainsight_components')
except:
    pass

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest',
        'test',
        'distutils',
        'setuptools',
        'pip',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GrainSight',
)
'''

    with open('grainsight.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("[OK] Spec file created: grainsight.spec")

def build_executable():
    """Build the executable using PyInstaller."""
    print("[INFO] Building executable...")
    
    # Clean previous builds
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("[INFO] Cleaned previous build")
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # Run PyInstaller
    cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'grainsight.spec']
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print("[ERROR] Build failed!")
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        return False
    
    print("[OK] Executable built successfully")
    return True

def create_installer_files():
    """Create installer files and documentation."""
    print("[INFO] Creating installer files...")
    
    dist_dir = Path('dist/GrainSight')
    if not dist_dir.exists():
        print("[ERROR] Distribution directory not found!")
        return False
    
    # Create launcher batch file
    launcher_content = '''@echo off
cd /d "%~dp0"
start "" "GrainSight.exe"
'''
    
    with open(dist_dir / 'Launch_GrainSight.bat', 'w') as f:
        f.write(launcher_content)
    
    # Create README file
    readme_content = '''GrainSight - Grain Analysis Software
=====================================

Welcome to GrainSight v5.0!

INSTALLATION:
1. Extract this folder to your desired location (e.g., C:\\Programs\\GrainSight)
2. Double-click "Launch_GrainSight.bat" to start the application
   OR
   Double-click "GrainSight.exe" directly

FIRST RUN:
- The application will automatically detect your system theme (light/dark)
- On first use, it may download required AI models (this is normal)
- Make sure you have an internet connection for the initial setup

SYSTEM REQUIREMENTS:
- Windows 10 or later (64-bit)
- At least 4GB RAM (8GB recommended)
- 2GB free disk space
- Graphics card with OpenGL support

FEATURES:
- Automatic grain detection and analysis
- Support for various image formats
- Real-time analysis with FastSAM AI model
- Export results to CSV and other formats
- Modern, adaptive user interface

TROUBLESHOOTING:
- If the application doesn\'t start, try running as administrator
- For Windows Defender warnings, add the folder to exclusions
- Check that all files are extracted properly

SUPPORT:
- For issues and updates, visit: https://github.com/your-repo/grainsight

Version: 5.0
Build Date: {build_date}
'''.format(build_date=__import__('datetime').datetime.now().strftime('%Y-%m-%d'))
    
    with open(dist_dir / 'README.txt', 'w') as f:
        f.write(readme_content)
    
    # Create simple installer script
    installer_content = '''@echo off
title GrainSight Setup
color 0A
echo.
echo  ================================================================
echo                      GrainSight Setup
echo                    Grain Analysis Software
echo  ================================================================
echo.
echo This will help you set up GrainSight on your computer.
echo.
echo INSTALLATION OPTIONS:
echo [1] Create desktop shortcut
echo [2] Add to Start Menu
echo [3] Both (Recommended)
echo [4] Skip shortcuts
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" goto desktop_only
if "%choice%"=="2" goto startmenu_only
if "%choice%"=="3" goto both
if "%choice%"=="4" goto skip
echo Invalid choice. Skipping shortcuts.
goto skip

:desktop_only
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut(\'%USERPROFILE%\\Desktop\\GrainSight.lnk\'); $Shortcut.TargetPath = \'%~dp0GrainSight.exe\'; $Shortcut.WorkingDirectory = \'%~dp0\'; $Shortcut.Description = \'GrainSight - Grain Analysis Tool\'; $Shortcut.Save()"
echo [OK] Desktop shortcut created!
goto done

:startmenu_only
echo Creating Start Menu shortcut...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut(\'%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight\\GrainSight.lnk\'); $Shortcut.TargetPath = \'%~dp0GrainSight.exe\'; $Shortcut.WorkingDirectory = \'%~dp0\'; $Shortcut.Description = \'GrainSight - Grain Analysis Tool\'; $Shortcut.Save()"
echo [OK] Start Menu shortcut created!
goto done

:both
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut(\'%USERPROFILE%\\Desktop\\GrainSight.lnk\'); $Shortcut.TargetPath = \'%~dp0GrainSight.exe\'; $Shortcut.WorkingDirectory = \'%~dp0\'; $Shortcut.Description = \'GrainSight - Grain Analysis Tool\'; $Shortcut.Save()"
echo Creating Start Menu shortcut...
if not exist "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight" mkdir "%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight"
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut(\'%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs\\GrainSight\\GrainSight.lnk\'); $Shortcut.TargetPath = \'%~dp0GrainSight.exe\'; $Shortcut.WorkingDirectory = \'%~dp0\'; $Shortcut.Description = \'GrainSight - Grain Analysis Tool\'; $Shortcut.Save()"
echo [OK] Both shortcuts created!
goto done

:skip
echo Skipping shortcut creation.

:done
echo.
echo ================================================================
echo                    Setup Complete!
echo ================================================================
echo.
echo GrainSight is ready to use!
echo.
echo To start the application:
echo • Double-click "Launch_GrainSight.bat"
echo • Or use the shortcuts you just created
echo • Or double-click "GrainSight.exe" directly
echo.
echo For help and documentation, see README.txt
echo.
echo Thank you for using GrainSight!
echo.
pause
'''
    
    with open(dist_dir / 'Setup.bat', 'w') as f:
        f.write(installer_content)
    
    print("[OK] Installer files created")
    return True

def create_distribution_package():
    """Create a ZIP package for easy distribution."""
    print("[INFO] Creating distribution package...")
    
    dist_dir = Path('dist/GrainSight')
    if not dist_dir.exists():
        print("[ERROR] Distribution directory not found!")
        return False
    
    # Create ZIP file
    zip_name = 'GrainSight_v5.0_Windows.zip'
    
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, 'dist')
                zipf.write(file_path, arc_name)
    
    print(f"[OK] Distribution package created: {zip_name}")
    print(f"[INFO] Package size: {os.path.getsize(zip_name) / (1024*1024):.1f} MB")
    return True

def main():
    """Main build process."""
    print("GrainSight Installer Builder")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("[ERROR] Requirements check failed!")
        return False
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("[ERROR] Build process failed!")
        return False
    
    # Create installer files
    if not create_installer_files():
        print("[ERROR] Failed to create installer files!")
        return False
    
    # Create distribution package
    if not create_distribution_package():
        print("[ERROR] Failed to create distribution package!")
        return False
    
    print("\n" + "=" * 50)
    print("BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print("\nFiles created:")
    print("   - dist/GrainSight/ - Application directory")
    print("   - GrainSight_v5.0_Windows.zip - Distribution package")
    print("\nFor users:")
    print("   1. Extract the ZIP file")
    print("   2. Run Setup.bat for shortcuts (optional)")
    print("   3. Use Launch_GrainSight.bat to start the app")
    print("\nReady for distribution!")
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n[ERROR] Build cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n[ERROR] Unexpected error: {e}")
        sys.exit(1)