2025-03-31 19:49:50,706 - __main__ - INFO - Application starting...
2025-03-31 19:49:50,706 - __main__ - INFO - Log file location: D:\Github\PetroSEG_V4\src\grainsight_components\logs\grainsight_app.log
2025-03-31 19:49:51,018 - gui.utils - INFO - Successfully loaded font: Arial from C:\Windows\Fonts\arial.ttf
2025-03-31 19:49:51,060 - __main__ - INFO - Using default application font: Arial
2025-03-31 19:49:51,060 - __main__ - INFO - Creating main application window...
2025-03-31 19:49:51,088 - gui.main_window - INFO - Initializing GrainAnalysisApp...
2025-03-31 19:49:51,388 - gui.main_window - WARNING - Application icon not found or invalid.
2025-03-31 19:49:51,468 - gui.main_window - INFO - Using device: cuda
2025-03-31 19:49:51,468 - gui.utils - INFO - Loaded theme preference: dark
2025-03-31 19:49:51,496 - __main__ - CRITICAL - Unhandled exception during application startup or execution: 'GrainAnalysisApp' object has no attribute 'crop_image'
Traceback (most recent call last):
  File "D:\Github\PetroSEG_V4\src\grainsight_components\main.py", line 93, in main
    window = GrainAnalysisApp()
             ^^^^^^^^^^^^^^^^^^
  File "D:\Github\PetroSEG_V4\src\grainsight_components\gui\main_window.py", line 137, in __init__
    self.setup_menu()
  File "D:\Github\PetroSEG_V4\src\grainsight_components\gui\main_window.py", line 418, in setup_menu
    self.crop_action = self._create_action(file_menu, "icons/crop.png", "&Crop Image...", self.crop_image, "Ctrl+X", "Crop the loaded image")
                                                                                          ^^^^^^^^^^^^^^^
AttributeError: 'GrainAnalysisApp' object has no attribute 'crop_image'
