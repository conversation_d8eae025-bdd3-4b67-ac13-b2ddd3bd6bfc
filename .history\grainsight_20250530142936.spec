# -*- mode: python ; coding: utf-8 -*-
import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Collect all data files and submodules
datas = []
hiddenimports = []

# Add source code as data files
datas += [('src', 'src')]

# Add configuration files
if os.path.exists('theme_config.json'):
    datas += [('theme_config.json', '.')]

# Add model files if they exist
if os.path.exists('src/grainsight_components/models'):
    datas += [('src/grainsight_components/models', 'src/grainsight_components/models')]

# Add icon files and assets
if os.path.exists('src/grainsight_components/gui/assets'):
    datas += [('src/grainsight_components/gui/assets', 'src/grainsight_components/gui/assets')]

# Collect PyTorch data files
try:
    datas += collect_data_files('torch')
    datas += collect_data_files('torchvision')
except:
    pass

# Collect Ultralytics data files
try:
    datas += collect_data_files('ultralytics')
except:
    pass

# Collect timm data files
try:
    datas += collect_data_files('timm')
except:
    pass

# Collect PySide6 data files
try:
    datas += collect_data_files('PySide6')
except:
    pass

# Collect jaraco data files
try:
    datas += collect_data_files('jaraco')
    datas += collect_data_files('jaraco.text')
    datas += collect_data_files('jaraco.functools') # Add other jaraco submodules if needed
except Exception as e:
    print(f"Warning: Could not collect jaraco data files: {e}")

# Hidden imports for modules that might not be detected
hiddenimports += [
    # Core Python modules
    'inspect',
    'importlib',
    'importlib.resources',
    'json',
    'logging',
    'threading',
    'time',
    'datetime',
    'locale',
    'math',
    'sys',
    'os',
    
    # Scientific computing
    'numpy',
    'pandas',
    'cv2',
    'PIL',
    'PIL.Image',
    'PIL.ImageTk',
    
    # PyTorch and related
    'torch',
    'torchvision',
    'torch.nn',
    'torch.optim',
    'torch.utils',
    'torch.utils.data',
    'timm',
    'timm.models',
    
    # Ultralytics YOLO
    'ultralytics',
    'ultralytics.models',
    'ultralytics.models.fastsam',
    'ultralytics.models.sam',
    
    # PySide6 GUI
    'PySide6.QtCore',
    'PySide6.QtGui',
    'PySide6.QtWidgets',
    'PySide6.QtOpenGL',
    'PySide6.QtOpenGLWidgets',
    
    # Matplotlib
    'matplotlib',
    'matplotlib.pyplot',
    'matplotlib.backends',
    'matplotlib.backends.backend_qt5agg',
    'matplotlib.figure',
    
    # Application modules
    'src.grainsight_components',
    'src.grainsight_components.gui',
    'src.grainsight_components.gui.main_window',
    'src.grainsight_components.gui.utils',
    'src.grainsight_components.config',
    'src.grainsight_components.analysis',
    'src.grainsight_components.models',

    # Jaraco and related for pkg_resources
    'jaraco',
    'jaraco.text',
    'jaraco.functools',
    'jaraco.collections',
    'jaraco.itertools',
    'jaraco.classes',
    'jaraco.context',
    'jaraco.path',
    'pkg_resources',
    'pkg_resources.extern',
    'pkg_resources._vendor',
    'pkg_resources._vendor.packaging'
]

# Collect all submodules from our source
try:
    hiddenimports += collect_submodules('src.grainsight_components')
except:
    pass

a = Analysis(
    ['grainsight.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'unittest',
        'test',
        'distutils',
        'setuptools',
        'pip',
    ],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='GrainSight',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='GrainSight',
)
