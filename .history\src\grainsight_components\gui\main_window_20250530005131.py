# gui/main_window.py
from typing import Optional

import sys
import os
import math
import json
import logging
import threading
import time
from datetime import datetime
import locale
import numpy as np
import pandas as pd
import torch
from ultralytics import YOLO # Keep for type checking? Or rely on core? Keep for now.
import cv2
# Import MobileSAM components directly
try:
    from ..mobile_sam import sam_model_registry, SamPredictor, SamAutomaticMaskGenerator
    MOBILE_SAM_AVAILABLE = True
except ImportError:
    MOBILE_SAM_AVAILABLE = False
    sam_model_registry = None
    SamPredictor = None
    SamAutomaticMaskGenerator = None


from PIL import Image # Keep PIL for image handling before display/passing to core

# PySide6 Imports
from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import (Qt, QSize, QPoint, QRect, QPointF, QRectF,
                           QItemSelectionModel, QItemSelection, Signal, Slot,
                           QObject, QThread, QTimer, QMetaObject, Q_ARG, Q_RETURN_ARG)
from PySide6.QtGui import (QPixmap, QImage, QIcon, QPainter, QPen, QColor,
                           QAction, QFont, QFontDatabase, QKeySequence, QPalette,
                           QStandardItemModel, QStandardItem, QDoubleValidator)
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QFileDialog, QMenu,
                               QMessageBox, QToolBar, QStatusBar, QVBoxLayout,
                               QLabel, QSlider, QCheckBox, QLineEdit, QPushButton,
                               QGraphicsView, QGraphicsScene, QGraphicsPixmapItem,
                               QGraphicsLineItem, QFrame, QGridLayout, QRadioButton,
                               QHBoxLayout, QScrollArea, QTreeView, QHeaderView,
                               QProgressDialog, QDialog, QAbstractItemView, QSizePolicy,
                               QGraphicsPolygonItem, QGraphicsTextItem, QTabWidget, QGroupBox,
                               QListWidget, QListWidgetItem, QSplitter, QGraphicsRectItem,
                               QToolButton, QProgressBar, QStackedWidget, QSpinBox)

# --- Local GUI Imports ---
from .widgets import (CustomGraphicsView, ResultsViewWidget,
                      FastSAMParameterWidget, MobileSAMParameterWidget, CustomPixmapItem)
from .widgets.parameter_widgets import DEFAULT_CONTOUR_THICKNESS
from .workers import ProcessingWorker, PatchProcessingWorker
from .dialogs import (show_plot_selection_dialog, show_crop_dialog,
                      show_about_dialog, PlotDisplayDialog, show_patch_config_dialog) # Assuming dialogs are refactored
from .utils import (pil_image_to_qimage, resource_path, load_theme_preference,
                    save_theme_preference, define_light_theme, define_dark_theme,
                    apply_stylesheet, find_system_font, load_font) # Assuming theme/utils refactored

# --- Core Logic Imports ---
# Import necessary functions/classes from the core module
from core.analysis import calculate_parameters # May only be needed by worker
from core.image_utils import create_segmented_visualization # May only be needed by worker
from core.io import generate_coco_dict

logger = logging.getLogger(__name__)

class GrainAnalysisApp(QMainWindow):

    # Define signals for cross-component communication
    # Signal to safely handle model loading between threads
    model_loaded_signal = Signal(object, str)  # Emits (model_object, model_type_str)
    model_load_failed_signal = Signal(str)     # Emits (error_message)
    # Signal for progress updates from worker threads
    progress_update_signal = Signal(int)       # Emits progress value (0-100)
    # Signal for showing error messages from worker threads
    error_message_signal = Signal(str, str)    # Emits (title, message)

    def __init__(self):
        super().__init__()
        logger.info("Initializing GrainAnalysisApp...")

        self.setWindowTitle("GrainSight")
        self.setGeometry(50, 50, 1500, 900) # Adjusted size

        # --- Icon ---
        try:
             app_icon = QIcon(resource_path("icons/grain_icon.png")) # Adjust icon name
             if not app_icon.isNull():
                  self.setWindowIcon(app_icon)
             else:
                  logger.warning("Application icon not found or invalid.")
        except Exception as e:
             logger.error(f"Error loading application icon: {e}")


        # --- State Variables ---
        self.uploaded_image: Optional[Image.Image] = None # Original PIL Image
        self.processed_image_vis: Optional[Image.Image] = None # PIL Image with visualization (contours)
        self.annotations: Optional[Union[list, torch.Tensor]] = None # List/Tensor of valid masks
        self.df: Optional[pd.DataFrame] = None # Pandas DataFrame with results
        self.current_scale_factor: Optional[float] = None # µm per pixel
        self.original_pixel_length: Optional[float] = None # Pixel length of scale line on original image
        self.image_file_path: Optional[str] = None # Path to the loaded image
        self.image_filename: Optional[str] = None # Filename of the loaded image
        self.last_save_dir: str = os.path.expanduser("~") # Default save dir
        self.default_save_dir: Optional[str] = None # User-defined default save directory

        self.grain_items: Dict[Any, Dict[str, Union[QGraphicsPolygonItem, QGraphicsTextItem]]] = {} # Mapping: df_index -> {'poly': QGraphicsPolygonItem, 'text': QGraphicsTextItem}
        # Selection state is now primarily managed within ResultsViewWidget and synced
        # self.selected_df_indices = set() # This state is mirrored in results_widget

        self.processing_thread: Optional[QThread] = None
        self.processing_worker: Optional[ProcessingWorker] = None
        self.patch_processing_thread: Optional[QThread] = None
        self.patch_processing_worker: Optional[PatchProcessingWorker] = None

        self.pixmap_item: Optional[CustomPixmapItem] = None # QGraphicsPixmapItem displaying the image

        self.plot_dialogs: List[PlotDisplayDialog] = [] # Track open plot dialogs

        self.model: Optional[object] = None # Loaded model (YOLO or base SAM)
        self.model_type: Optional[str] = None # Track the currently loaded model type ('fastsam' or 'mobilesam')
        self.device: torch.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        logger.info(f"Using device: {self.device}")

        # --- Theme ---
        self.theme_file = os.path.join(os.path.dirname(__file__), "..", "config", "theme_config.json") # Path relative to this file
        self.current_theme = load_theme_preference(self.theme_file)
        self.apply_theme(self.current_theme) # Apply theme early

        # --- UI Setup ---
        self.setup_menu()
        self.setup_toolbar()
        self.setup_widgets() # This now sets the central widget and creates sub-widgets
        self.setup_status_bar()

        # --- Connect Signals/Slots from Custom Widgets ---
        self.view.scale_line_drawn.connect(self.on_scale_line_drawn)
        self.view.scene_clicked.connect(self.on_scene_clicked)
        self.view.zoom_changed.connect(self.update_action_states) # Update actions on zoom change maybe?

        self.results_widget.selection_changed.connect(self.on_results_view_selection_changed)
        self.results_widget.delete_requested.connect(self.delete_selected_grains)

        # Connect parameter widget changes if needed (e.g., live update validation)
        # self.fastsam_params_widget.parameters_changed.connect(self.on_parameters_changed)
        # self.mobilesam_params_widget.parameters_changed.connect(self.on_parameters_changed)

        # --- Connect Model Loading Signals ---
        self.model_loaded_signal.connect(self._handle_model_loaded)
        self.model_load_failed_signal.connect(self._handle_model_load_failed)
        # Connect progress and error signals
        self.progress_update_signal.connect(self.update_progress)
        self.error_message_signal.connect(self._show_error_message)

        # --- Model Loading ---
        self.load_model_async() # Start loading model in background

        # --- Final UI State ---
        self.update_action_states()
        logger.info("GrainAnalysisApp initialization complete.")


    # --- Model Loading Methods ---

    def load_model_async(self):
        """Loads the selected model in a separate thread."""
        if self.model is not None:
             logger.info("Model already loaded or loading.")
             # Optionally ask user if they want to reload
             # return

        self.update_status("Loading model...")
        self.update_action_states(processing=True) # Disable controls during load

        # Use standard threading for simplicity here
        thread = threading.Thread(target=self._load_model_worker, daemon=True)
        thread.start()

    def on_model_type_changed(self):
        """Handles model type radio button changes."""
        selected_type = 'fastsam' if self.fastsam_radio.isChecked() else 'mobilesam'
        logger.debug(f"Model type selection changed to: {selected_type}")

        # Switch the parameter widget stack
        self.model_params_stack.setCurrentWidget(
            self.fastsam_params_widget if selected_type == 'fastsam' else self.mobilesam_params_widget
        )

        # Reload model only if the *loaded* model type is different
        if self.model is not None and selected_type != self.model_type:
            reply = QMessageBox.question(
                self, "Change Model Type",
                f"This will unload the current {self.model_type.upper()} model "
                f"and load the {selected_type.upper()} model.\nContinue?",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                logger.info(f"Reloading model for type: {selected_type}")
                self.model = None  # Clear current model reference
                self.model_type = None
                self.load_model_async()  # Reload with new type
            else:
                # Revert radio button selection if canceled
                self._update_model_radio_buttons(self.model_type == 'fastsam')
        # If no model loaded yet, just update UI, load will happen based on current selection
        elif self.model is None:
             logger.info("Model type selection changed, but no model loaded yet.")


    def _load_model_worker(self):
        """Worker function to load the selected model (runs in thread)."""
        model_to_load = None
        loaded_type = None
        error_msg = None
        use_fastsam = False  # Default value

        try:
            # Get the desired model type from UI (use invokeMethod for thread safety)
            use_fastsam = QMetaObject.invokeMethod(
                self, "_get_fastsam_selection", Qt.BlockingQueuedConnection,
                Q_RETURN_ARG(bool) # Get return value
            )

            if use_fastsam:
                # --- Load FastSAM ---
                model_dir = os.path.join(os.path.dirname(__file__), '..', 'models') # Relative path
                model_path = os.path.join(model_dir, "FastSAM-x.pt") # Adjust filename if needed
                if not os.path.exists(model_path):
                    error_msg = f"FastSAM model file not found at expected location: {model_path}"
                    logger.error(error_msg)
                    # Create models directory if it doesn't exist
                    os.makedirs(model_dir, exist_ok=True)
                    # Show a more helpful error message with download instructions
                    error_msg = f"FastSAM model file not found at: {model_path}\n\nPlease download the model file from https://github.com/CASIA-IVA-Lab/FastSAM/releases and place it in the models directory.\n\nThe application will continue to load, but segmentation functionality will be limited until the model is installed."

                    # Switch to MobileSAM if available as a fallback
                    if MOBILE_SAM_AVAILABLE:
                        QMetaObject.invokeMethod(self, "_switch_to_mobilesam", Qt.QueuedConnection)
                        logger.info("Switching to MobileSAM as fallback due to missing FastSAM model.")
                        # We'll still return the error for now, but UI will be updated
                else:
                    logger.info(f"Loading FastSAM model from: {model_path}")
                    # Load model initially on CPU
                    try:
                        model_to_load = YOLO(model_path)
                        loaded_type = "fastsam"
                        logger.info("FastSAM model loaded successfully (on CPU).")
                    except Exception as model_load_e:
                        error_msg = f"Failed to load FastSAM model: {model_load_e}"
                        logger.error(error_msg, exc_info=True)

            else:
                # --- Load MobileSAM (Base Model) ---
                 if not MOBILE_SAM_AVAILABLE:
                      error_msg = "MobileSAM library components not found. Please install 'mobile_sam'."
                      logger.error(error_msg)
                 else:
                      weights_dir = os.path.join(os.path.dirname(__file__), '..', 'weights') # Relative path
                      model_path = os.path.join(weights_dir, "mobile_sam.pt")
                      if not os.path.exists(model_path):
                           error_msg = f"MobileSAM weights file not found at expected location: {model_path}"
                           logger.error(error_msg)
                           # Create weights directory if it doesn't exist
                           os.makedirs(weights_dir, exist_ok=True)
                           # Show a more helpful error message with download instructions
                           error_msg = f"MobileSAM weights file not found at: {model_path}\n\nPlease download the weights file from https://github.com/ChaoningZhang/MobileSAM/tree/master/weights and place it in the weights directory."
                      else:
                           logger.info(f"Loading MobileSAM base model from: {model_path}")
                           # Load base SAM model on CPU
                           try:
                               # Get the sam_model_registry from our wrapper
                               sam_model_registry_obj = get_sam_model_registry()
                               if sam_model_registry_obj is None:
                                   error_msg = "Failed to get sam_model_registry from wrapper"
                                   logger.error(error_msg)
                               else:
                                   # Load base SAM model on CPU
                                   sam = sam_model_registry_obj["vit_t"](checkpoint=model_path)
                                   model_to_load = sam
                                   loaded_type = "mobilesam"
                                   logger.info("MobileSAM base model loaded successfully (on CPU).")
                           except Exception as model_load_e:
                               error_msg = f"Failed to load MobileSAM model: {model_load_e}"
                               logger.error(error_msg, exc_info=True)

        except Exception as e:
            logger.exception("Exception during model loading worker.")
            error_msg = f"Failed to load model: {e}"

        # --- Emit Signal to Update GUI Thread ---
        if error_msg:
            # Emit failure signal
            self.model_load_failed_signal.emit(error_msg)
        elif model_to_load and loaded_type:
            # Emit success signal with the loaded objects
            self.model_loaded_signal.emit(model_to_load, loaded_type)
        else:
            # Should not happen if loading was attempted, but handle anyway
            self.model_load_failed_signal.emit("Unknown error during model loading.")


    @Slot(result=bool) # Helper slot to get UI state thread-safely
    def _get_fastsam_selection(self) -> bool:
        return self.fastsam_radio.isChecked()

    @Slot() # Helper slot to switch to MobileSAM when FastSAM is not available
    def _switch_to_mobilesam(self):
        """Switches the UI to use MobileSAM when FastSAM is not available."""
        if hasattr(self, 'mobilesam_radio') and not self.mobilesam_radio.isChecked():
            self.mobilesam_radio.setChecked(True)
            # This will trigger on_model_type_changed if connected
            self.on_model_type_changed()
            # Show a notification to the user
            QMessageBox.information(
                self, "Model Switched",
                "Switched to MobileSAM because FastSAM model file was not found.\n\n"
                "You can still use the application with MobileSAM, but for full functionality, "
                "please download the FastSAM model as instructed."
            )

    @Slot(object, str) # Slot for model_loaded_signal
    def _handle_model_loaded(self, loaded_model, loaded_type):
        """Handles successful model loading."""
        self.model = loaded_model
        self.model_type = loaded_type
        status = f"{self.model_type.upper()} model loaded. Ready."
        self.update_status(status)
        self._update_model_radio_buttons(self.model_type == 'fastsam')
        self.update_action_states() # Update button enabled/disabled states
        logger.info(f"Successfully handled loaded model: {loaded_type}")


    @Slot(str) # Slot for model_load_failed_signal
    def _handle_model_load_failed(self, error_message):
        """Handles model loading failure."""
        self.model = None
        self.model_type = None
        self._show_error_message("Model Load Error", error_message)
        self.update_status(f"Error loading model: {error_message}")
        self._update_model_radio_buttons(self.fastsam_radio.isChecked()) # Keep UI selection consistent
        self.update_action_states() # Update button enabled/disabled states
        logger.error(f"Model loading failed: {error_message}")

    @Slot(bool) # Slot to update radio buttons safely from other threads or internally
    def _update_model_radio_buttons(self, use_fastsam: bool):
        """Updates the model radio buttons and stacked widget."""
        # Block signals to prevent triggering on_model_type_changed during update
        self.fastsam_radio.blockSignals(True)
        self.mobilesam_radio.blockSignals(True)

        self.fastsam_radio.setChecked(use_fastsam)
        self.mobilesam_radio.setChecked(not use_fastsam)

        # Ensure the correct parameter widget is shown
        self.model_params_stack.setCurrentWidget(
            self.fastsam_params_widget if use_fastsam else self.mobilesam_params_widget
        )

        # Unblock signals
        self.fastsam_radio.blockSignals(False)
        self.mobilesam_radio.blockSignals(False)
        logger.debug(f"UI updated for {'FastSAM' if use_fastsam else 'MobileSAM'} model selection.")


    # --- Theme Methods ---
    # (Keep define_light_theme, define_dark_theme, apply_theme as before, but use apply_stylesheet)
    def apply_theme(self, theme: str):
        """Applies the specified theme palette and stylesheet."""
        if theme == 'light':
            palette = define_light_theme()
            apply_stylesheet(self, theme='light') # Use utility function
        elif theme == 'dark':
            palette = define_dark_theme()
            apply_stylesheet(self, theme='dark') # Use utility function
        else: # Fallback to light
             palette = define_light_theme()
             apply_stylesheet(self, theme='light')

        QApplication.setPalette(palette)
        self.setPalette(palette)
        self.update_widget_styles()

    def toggle_dark_mode(self):
        if self.current_theme == 'light':
            self.current_theme = 'dark'
        else:
            self.current_theme = 'light'
        logger.info(f"Toggling theme to {self.current_theme}.")
        self.apply_theme(self.current_theme)
        save_theme_preference(self.theme_file, self.current_theme)
        self.update_plot_themes() # Notify plots theme changed


    def update_widget_styles(self):
        """Updates specific widget styles after theme change."""
        if hasattr(self, 'view'): # Ensure view exists
            bg_color = self.palette().color(QPalette.ColorRole.Base) # Use Base color for background
            self.view.setBackgroundBrush(bg_color)
        # Update plot backgrounds if they are open and tracked
        self.update_plot_themes()

    def update_plot_themes(self):
         """Attempts to update the theme of existing plot dialogs."""
         if not self.plot_dialogs: return
         logger.warning("Theme changed. For plots to fully reflect the new theme, please close and regenerate them.")
         # You could iterate through self.plot_dialogs and call a hypothetical update_theme method
         # for dialog in self.plot_dialogs:
         #     if hasattr(dialog, 'update_theme'):
         #         dialog.update_theme()


    # --- UI Setup Methods ---
    def setup_menu(self):
        menubar = self.menuBar()

        # --- File Menu ---
        file_menu = menubar.addMenu("&File")
        # upload_action removed - images are now only loaded from project hub
        self.crop_action = self._create_action(file_menu, "icons/crop.png", "&Crop Image...", self.crop_image, "Ctrl+X", "Crop the loaded image")
        file_menu.addSeparator()
        self.save_results_action = self._create_action(file_menu, "icons/save.png", "&Save Results...", self.save_results, QKeySequence.Save, "Save analysis data (CSV) and image (PNG)")
        self.save_view_action = self._create_action(file_menu, "icons/save_view.png", "Save Current &View...", self.save_current_view, "Ctrl+Shift+S", "Save the current image view")
        self.export_coco_action = self._create_action(file_menu, "icons/coco.png", "Export &COCO Annotations...", self.save_coco_annotations, status_tip="Save masks in COCO JSON format")
        file_menu.addSeparator()
        exit_action = self._create_action(file_menu, "icons/exit.png", "E&xit", self.close, QKeySequence.Quit, "Exit application")

        # --- Edit Menu ---
        edit_menu = menubar.addMenu("&Edit")
        self.reset_action = self._create_action(edit_menu, "icons/reset.png", "&Reset State", self.reset_app, status_tip="Reset image, results, parameters")

        # --- View Menu ---
        view_menu = menubar.addMenu("&View")
        theme_action = self._create_action(view_menu, None, "Toggle &Dark/Light Mode", self.toggle_dark_mode, status_tip="Switch between dark/light UI themes")
        view_menu.addSeparator()
        # Zoom actions connect to view slots directly
        self.zoom_in_action = self._create_action(view_menu, "icons/zoom_in.png", "Zoom &In", lambda: self.view.wheelEvent(QtGui.QWheelEvent(QPoint(0,0), QPoint(0,0), QPoint(0,0), QPoint(0,120), Qt.MouseButton.NoButton, Qt.KeyboardModifier.NoModifier, Qt.ScrollPhase.ScrollUpdate, False)), QKeySequence.ZoomIn, "Zoom in") # Simulate wheel event
        self.zoom_out_action = self._create_action(view_menu, "icons/zoom_out.png", "Zoom &Out", lambda: self.view.wheelEvent(QtGui.QWheelEvent(QPoint(0,0), QPoint(0,0), QPoint(0,0), QPoint(0,-120), Qt.MouseButton.NoButton, Qt.KeyboardModifier.NoModifier, Qt.ScrollPhase.ScrollUpdate, False)), QKeySequence.ZoomOut, "Zoom out") # Simulate wheel event
        self.reset_zoom_action = self._create_action(view_menu, "icons/zoom_reset.png", "Reset &Zoom", self.reset_view_zoom, "Ctrl+0", "Reset zoom and fit image")

        # --- Tools Menu ---
        tools_menu = menubar.addMenu("&Tools")
        self.plot_action = self._create_action(tools_menu, "icons/plot.png", "&Generate Plots...", self.show_plotting_dialog, "Ctrl+P", "Generate plots from results")
        self.segment_action = self._create_action(tools_menu, "icons/process.png", "Segment &Image...", self.start_processing, status_tip="Segment image in one pass (alternative method)")
        self.segment_action.setEnabled(False) # Disable segment action

        # --- Settings Menu ---
        settings_menu = menubar.addMenu("&Settings")
        set_default_save_dir_action = self._create_action(settings_menu, None, "Set Default Save &Directory...", self.set_default_save_directory, status_tip="Set default folder for saving files")

        # --- Help Menu ---
        help_menu = menubar.addMenu("&Help")
        about_action = self._create_action(help_menu, None, "&About GrainSight", lambda: show_about_dialog(self), status_tip="Show application information")


    def _create_action(self, parent_menu, icon_path, text, slot, shortcut=None, status_tip=None):
        """Helper to create QAction."""
        icon = QIcon(resource_path(icon_path)) if icon_path else QIcon()
        action = QAction(icon, text, self)
        if slot:
            action.triggered.connect(slot)
        if shortcut:
            action.setShortcut(QKeySequence(shortcut))
        if status_tip:
            action.setStatusTip(status_tip)
        parent_menu.addAction(action)
        return action


    def setup_toolbar(self):
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # Add actions using helper or directly
        # upload_action removed - images are now only loaded from project hub
        toolbar.addAction(self.crop_action)
        toolbar.addSeparator()
        toolbar.addAction(self.save_results_action)
        toolbar.addAction(self.export_coco_action)
        toolbar.addSeparator()
        toolbar.addAction(self.plot_action)
        toolbar.addAction(self.reset_action)
        toolbar.addSeparator()
        toolbar.addAction(self.zoom_in_action)
        toolbar.addAction(self.zoom_out_action)
        toolbar.addAction(self.reset_zoom_action)
        toolbar.addSeparator()

        # Spacer
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(spacer)

        # --- Scale Factor Toolbar Group ---
        scale_toolbar_group = QGroupBox("Scale")
        scale_toolbar_layout = QHBoxLayout(scale_toolbar_group)
        scale_toolbar_layout.setContentsMargins(2,2,2,2)
        scale_toolbar_layout.setSpacing(3)

        self.save_scale_action = QAction(QIcon(resource_path("icons/save_scale.png")), "Save Scale", self)
        self.save_scale_action.triggered.connect(self.save_scale_factor)
        self.save_scale_action.setStatusTip("Save current scale factor to JSON")
        save_scale_button = QToolButton()
        save_scale_button.setDefaultAction(self.save_scale_action)
        scale_toolbar_layout.addWidget(save_scale_button)

        self.load_scale_action = QAction(QIcon(resource_path("icons/load_scale.png")), "Load Scale", self)
        self.load_scale_action.triggered.connect(self.load_scale_factor)
        self.load_scale_action.setStatusTip("Load scale factor from JSON")
        load_scale_button = QToolButton()
        load_scale_button.setDefaultAction(self.load_scale_action)
        scale_toolbar_layout.addWidget(load_scale_button)

        self.reset_scale_action = QAction(QIcon(resource_path("icons/reset_scale.png")), "Reset Scale", self)
        self.reset_scale_action.triggered.connect(self.reset_scale)
        self.reset_scale_action.setStatusTip("Clear current scale factor")
        reset_scale_button = QToolButton()
        reset_scale_button.setDefaultAction(self.reset_scale_action)
        scale_toolbar_layout.addWidget(reset_scale_button)

        toolbar.addWidget(scale_toolbar_group)


        # --- Mode Selection Toolbar Group ---
        mode_group = QGroupBox("Mode")
        mode_layout = QHBoxLayout(mode_group)
        mode_layout.setContentsMargins(2,2,2,2)
        mode_layout.setSpacing(3)

        self.mode_buttons = {}
        modes = [
            (CustomGraphicsView.MODE_SELECTION, "Select", "icons/select.png", "Select grains"),
            (CustomGraphicsView.MODE_SCALE, "Scale", "icons/scale.png", "Draw scale line"),
            (CustomGraphicsView.MODE_PAN, "Pan", "icons/pan.png", "Pan image")
        ]

        for key, text, icon_path, tip in modes:
            radio_btn = QRadioButton(text)
            try:
                 icon = QIcon(resource_path(icon_path))
                 if not icon.isNull(): radio_btn.setIcon(icon)
            except Exception as e: logger.error(f"Error loading icon {icon_path}: {e}")

            radio_btn.setStatusTip(tip)
            # Store the radio button for later connection
            self.mode_buttons[key] = radio_btn
            mode_layout.addWidget(radio_btn)
            self.mode_buttons[key] = radio_btn

        # Set initial selection mode checked but don't connect signals yet
        # (Will connect after view is created in setup_widgets)
        if CustomGraphicsView.MODE_SELECTION in self.mode_buttons:
            self.mode_buttons[CustomGraphicsView.MODE_SELECTION].setChecked(True)

        toolbar.addWidget(mode_group)

    def setup_widgets(self):
        # Main container (Splitter)
        main_container = QSplitter(Qt.Horizontal, self)
        self.setCentralWidget(main_container)

        # --- Left Panel (Controls) ---
        left_frame = QFrame()
        left_layout = QVBoxLayout(left_frame)
        left_frame.setMinimumWidth(350)
        left_frame.setMaximumWidth(450)
        main_container.addWidget(left_frame)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        control_widget_container = QWidget() # Widget inside scroll area
        control_layout = QVBoxLayout(control_widget_container)
        scroll_area.setWidget(control_widget_container)
        left_layout.addWidget(scroll_area)

        # --- Model Type Selection ---
        model_type_group = QGroupBox("Segmentation Model")
        model_type_layout = QHBoxLayout(model_type_group)
        self.fastsam_radio = QRadioButton("FastSAM")
        self.mobilesam_radio = QRadioButton("MobileSAM")
        if not MOBILE_SAM_AVAILABLE:
             self.mobilesam_radio.setEnabled(False)
             self.mobilesam_radio.setToolTip("MobileSAM library not detected.")
        self.fastsam_radio.setChecked(True) # Default
        model_type_layout.addWidget(self.fastsam_radio)
        model_type_layout.addWidget(self.mobilesam_radio)
        control_layout.addWidget(model_type_group)
        # Connect model type change handler
        self.fastsam_radio.toggled.connect(self.on_model_type_changed)
        # Mobilesam toggled will also trigger it via the fastsam check state changing


        # --- Model Parameters Stack ---
        param_group = QGroupBox("Model Parameters")
        param_group_layout = QVBoxLayout(param_group) # Use QVBoxLayout for stack
        control_layout.addWidget(param_group)

        self.model_params_stack = QStackedWidget()
        param_group_layout.addWidget(self.model_params_stack)

        # Create and add parameter widgets to stack
        self.fastsam_params_widget = FastSAMParameterWidget()
        self.mobilesam_params_widget = MobileSAMParameterWidget()
        if not MOBILE_SAM_AVAILABLE:
            # If MobileSAM not available, we might want to hide or disable its params
            # For now, we just disable the radio button. Adding widget is harmless.
             pass

        self.model_params_stack.addWidget(self.fastsam_params_widget)
        self.model_params_stack.addWidget(self.mobilesam_params_widget)


        # --- Scale Calculation Group ---
        scale_group = QGroupBox("Image Scale")
        scale_layout = QVBoxLayout(scale_group)
        control_layout.addWidget(scale_group)

        scale_layout.addWidget(QLabel("<b>Method 1: Draw Scale Line</b>"))
        scale_instruct_layout = QHBoxLayout()
        scale_instruct_layout.addWidget(QLabel("Real-world length (µm):"))
        self.real_world_length_edit = QLineEdit("100")
        self.real_world_length_edit.setValidator(QDoubleValidator(0.01, 1000000.0, 3))
        scale_instruct_layout.addWidget(self.real_world_length_edit)
        scale_layout.addLayout(scale_instruct_layout)
        scale_layout.addWidget(QLabel("<i>Then select 'Scale' mode and draw on image.</i>"))

        scale_layout.addWidget(QLabel("<hr><b>Method 2: Enter Scale Directly</b>"))
        manual_scale_layout = QHBoxLayout()
        manual_scale_layout.addWidget(QLabel("Scale (µm/pixel):"))
        self.manual_scale_edit = QLineEdit("0.0")
        self.manual_scale_edit.setValidator(QDoubleValidator(0.000001, 1000.0, 6))
        self.manual_scale_edit.setFixedWidth(120)
        manual_scale_layout.addWidget(self.manual_scale_edit)
        set_scale_button = QPushButton("Set")
        set_scale_button.setObjectName("setManualScaleButton")
        set_scale_button.setToolTip("Apply the manually entered scale factor")
        set_scale_button.clicked.connect(self.set_manual_scale)
        manual_scale_layout.addWidget(set_scale_button)
        manual_scale_layout.addStretch()
        scale_layout.addLayout(manual_scale_layout)

        self.scale_factor_label = QLabel("<b>Current Scale: Not set</b>")
        self.scale_factor_label.setStyleSheet("padding-top: 5px;")
        scale_layout.addWidget(self.scale_factor_label)

        # --- Action Buttons ---
        action_layout = QVBoxLayout()
        control_layout.addLayout(action_layout)

        self.process_button = QPushButton(QIcon(resource_path("icons/grid.png")), " Segment in Patches")
        self.process_button.setToolTip("Segment large images using patches (default method)")
        self.process_button.clicked.connect(self.analyze_with_patches)
        action_layout.addWidget(self.process_button)

        self.recalculate_button = QPushButton(QIcon(resource_path("icons/recalculate.png")), " Recalculate Parameters")
        self.recalculate_button.setToolTip("Recalculate parameters using current scale")
        self.recalculate_button.clicked.connect(self.recalculate_parameters_with_new_scale)
        action_layout.addWidget(self.recalculate_button)

        control_layout.addStretch() # Push controls upwards

        # --- Right Panel (Image and Results) ---
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        main_container.addWidget(right_frame)

        # Image Display (Custom Graphics View)
        self.view = CustomGraphicsView(self)
        self.scene = QGraphicsScene(self)
        self.view.setScene(self.scene)
        bg_color = self.palette().color(QPalette.ColorRole.Base) # Use theme base color
        self.view.setBackgroundBrush(bg_color)
        right_layout.addWidget(self.view, 5) # Stretch factor for view

        # Scale reminder label (below view)
        self.scale_reminder_label = QLabel("Load image and set scale.")
        self.scale_reminder_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(self.scale_reminder_label)
        self.update_scale_reminder() # Set initial style/text

        # Results Display (ResultsViewWidget)
        self.results_widget = ResultsViewWidget(self)
        right_layout.addWidget(self.results_widget, 3) # Stretch factor for results

        # --- Splitter Sizes ---
        QTimer.singleShot(100, lambda: main_container.setSizes([int(self.width() * 0.3), int(self.width() * 0.7)]))

        # Connect mode radio buttons to view now that it's initialized
        for key, radio_btn in self.mode_buttons.items():
            # Disconnect any existing connections first to avoid duplicates
            try:
                radio_btn.toggled.disconnect()
            except RuntimeError:
                # No connections to disconnect
                pass

            # Create a more robust connection that ensures mode switching works
            radio_btn.toggled.connect(lambda checked, m=key: self._handle_mode_button_toggled(checked, m))


    def _handle_mode_button_toggled(self, checked, mode):
        """Handle mode button toggling with better error handling."""
        if checked:
            logger.info(f"Mode button toggled: switching to {mode} mode")
            try:
                # Force update the view mode
                self.view.set_mode(mode)
                # Update UI state
                self.update_action_states()
                # Log success
                logger.debug(f"Successfully switched to {mode} mode")
            except Exception as e:
                logger.error(f"Error switching to {mode} mode: {e}")
                self._show_error_message("Mode Switch Error", f"Could not switch to {mode} mode. Try reloading the image.")

    def setup_status_bar(self):
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_progress = QProgressBar()
        self.status_progress.setMaximumSize(200, 20)  # Increased size for better visibility
        self.status_progress.setMinimumHeight(20)  # Ensure minimum height
        self.status_progress.setTextVisible(True)  # Show percentage text
        self.status_progress.setStyleSheet("QProgressBar { min-height: 20px; font-weight: bold; }")
        self.status_bar.addPermanentWidget(self.status_progress)
        self.status_progress.hide() # Hide initially
        self.update_status("Ready. Load an image to start.")

    # --- Core Logic Slots / Methods ---

    @Slot()
    def reset_app_state(self, clear_image=True):
         """Resets analysis data and optionally the loaded image."""
         logger.info(f"Resetting application state (clear_image={clear_image}).")
         self.stop_processing_thread()

         if clear_image:
              self.uploaded_image = None
              self.image_file_path = None
              self.image_filename = None
              self.scene.clear() # Clear scene contents
              self.pixmap_item = None
              self.view.reset_view() # Reset zoom/pan

         self.processed_image_vis = None
         self.annotations = None # Use None instead of [] for consistency
         self.df = None
         self.grain_items = {}
         # self.selected_df_indices = set() # Selection managed by results_widget

         # Reset scale
         self.current_scale_factor = None
         self.original_pixel_length = None
         self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
         self.view.set_scale_line_points(None, None) # Clear points in view
         self.update_scale_reminder()

         # Clear results table via its widget
         self.results_widget.clear()

         self.update_action_states()
         self.update_status("State reset." + (" Load an image." if clear_image else ""))

    @Slot()
    def reset_app(self):
        """Resets the application to its initial state, asking for confirmation."""
        if self.df is not None or self.uploaded_image is not None:
            reply = QMessageBox.question(
                self, "Reset Confirmation",
                "Reset all data (image, results, scale)?\nUnsaved data will be lost.",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
            )
            if reply == QMessageBox.Cancel: return

        logger.info("Performing full application reset.")
        self.reset_app_state(clear_image=True)
        # Reset UI parameter defaults
        self.fastsam_params_widget.reset_defaults()
        if MOBILE_SAM_AVAILABLE:
             self.mobilesam_params_widget.reset_defaults()
        self.real_world_length_edit.setText("100")
        self.manual_scale_edit.setText("0.0")
        # Reset model selection? Keep current UI selection or default to FastSAM? Let's default.
        self.fastsam_radio.setChecked(True)

        self.update_status("Application reset. Load an image to start.")

    @Slot()
    def upload_image(self):
        """Opens a file dialog for the user to select an image."""
        if self.processing_thread and self.processing_thread.isRunning():
             # ... (confirm stop processing) ...
             if QMessageBox.question(self, "Confirm", "Processing ongoing. Stop and load new image?",
                                     QMessageBox.Yes | QMessageBox.Cancel) == QMessageBox.Cancel:
                  return
             self.stop_processing_thread()

        file_path, _ = QFileDialog.getOpenFileName(
            self, "Open Image File", self.last_save_dir,
            "Image Files (*.png *.jpg *.jpeg *.tif *.tiff *.bmp);;All Files (*)",
        )
        if not file_path: return

        try:
            self.update_status(f"Loading image: {os.path.basename(file_path)}...")
            QApplication.setOverrideCursor(Qt.WaitCursor)

            new_image = Image.open(file_path)
            # Convert to RGB for consistency (core functions expect RGB)
            if new_image.mode == 'L':
                new_image = new_image.convert('RGB')
            elif new_image.mode == 'RGBA':
                 # Keep alpha for display, but might need RGB for processing
                 # Let's convert to RGB immediately for simplicity with core functions
                 new_image = new_image.convert('RGB')
            elif new_image.mode != 'RGB':
                 logger.warning(f"Converting image mode {new_image.mode} to RGB.")
                 new_image = new_image.convert('RGB')


            # --- State Reset for New Image ---
            self.reset_app_state(clear_image=True) # Full reset

            self.uploaded_image = new_image
            self.image_file_path = file_path
            self.image_filename = os.path.basename(file_path)
            self.last_save_dir = os.path.dirname(file_path)

            logger.info(f"Image loaded: {self.image_filename} ({self.uploaded_image.width}x{self.uploaded_image.height})")

            # Display the image (using helper now)
            self.display_image_on_scene(self.uploaded_image)

            self.update_action_states()
            self.update_scale_reminder()
            self.update_status(f"Image '{self.image_filename}' loaded. Set scale.")
            self.setWindowTitle(f"GrainSight - {self.image_filename}")

            # Suggest input size (optional)
            min_dim = min(self.uploaded_image.width, self.uploaded_image.height)
            rec_input_size = max(256, min(2048, int(min_dim * 0.95)))
            rec_input_size = max(256, (rec_input_size // 64) * 64) # Nearest multiple of 64
            # Update FastSAM widget directly (if it exists)
            if hasattr(self, 'fastsam_params_widget'):
                 self.fastsam_params_widget.input_size_slider.setValue(rec_input_size)
            logger.debug(f"Suggested input size: {rec_input_size}")


        except FileNotFoundError:
            self._show_error_message("Error", f"Image file not found: {file_path}")
            self.update_status("Error: Image file not found.")
        except Exception as e:
             logger.exception(f"Failed to load image: {file_path}")
             self._show_error_message("Image Load Error", f"Could not load image file.\nError: {e}")
             self.reset_app_state(clear_image=True) # Reset if load failed
             self.update_status("Error loading image.")
        finally:
            QApplication.restoreOverrideCursor()


    def display_image_on_scene(self, pil_image_to_display: Optional[Image.Image]):
        """Clears the scene and displays the given PIL image."""
        self.scene.clear() # Clear everything first
        self.pixmap_item = None
        self.grain_items = {} # Clear grain graphics item tracking

        if pil_image_to_display is None:
            logger.warning("Attempted to display a None image.")
            self.view.reset_view() # Reset view even if empty
            return

        qimage = pil_image_to_qimage(pil_image_to_display) # Use utility function
        if qimage is None or qimage.isNull():
            logger.error("Failed to convert PIL to QImage or QImage is null.")
            self._show_error_message("Display Error", "Could not convert image for display.")
            return

        pixmap = QPixmap.fromImage(qimage)
        if pixmap.isNull():
            logger.error("Created QPixmap is null!")
            self._show_error_message("Display Error", "Failed to create pixmap from image data.")
            return

        self.pixmap_item = CustomPixmapItem(pixmap) # Use custom item
        self.scene.addItem(self.pixmap_item)
        self.scene.setSceneRect(self.pixmap_item.boundingRect()) # Set scene size

        self.view.reset_view() # Reset zoom/pan and fitInView

        logger.debug(f"Displayed image ({pil_image_to_display.width}x{pil_image_to_display.height}) on scene.")

    def clear_scene_graphics_items(self, clear_pixmap=False):
         """Removes grain polygons, text, scale lines, etc. Optionally the base image."""
         items_to_remove = []
         for item in self.scene.items():
              # Remove highlights, text, scale lines
              if isinstance(item, (QGraphicsPolygonItem, QGraphicsTextItem, QGraphicsLineItem)):
                   items_to_remove.append(item)
              elif clear_pixmap and item == self.pixmap_item:
                   items_to_remove.append(item)

         logger.debug(f"Removing {len(items_to_remove)} graphics items (clear_pixmap={clear_pixmap}).")
         for item in items_to_remove:
              self.scene.removeItem(item)

         if clear_pixmap:
             self.pixmap_item = None
         self.grain_items = {} # Clear grain item tracking
         # Do NOT clear scale line item reference in the view here, manage it separately
         # self.view.clear_scale_line_item()


    @Slot(QPointF, QPointF)
    def on_scale_line_drawn(self, start_point_scene: QPointF, end_point_scene: QPointF):
        """Handles the scale line drawn signal from the view."""
        if self.uploaded_image is None or self.pixmap_item is None: return

        try:
            # --- Map Scene Coords to Original Image Coords ---
            display_width = self.pixmap_item.pixmap().width()
            display_height = self.pixmap_item.pixmap().height()
            original_width = self.uploaded_image.width
            original_height = self.uploaded_image.height
            if display_width == 0 or display_height == 0: raise ValueError("Invalid pixmap dimensions")

            ratio_x = original_width / display_width
            ratio_y = original_height / display_height

            start_orig = QPointF(start_point_scene.x() * ratio_x, start_point_scene.y() * ratio_y)
            end_orig = QPointF(end_point_scene.x() * ratio_x, end_point_scene.y() * ratio_y)

            # Calculate pixel length on original image
            # Using QLineF for distance calculation
            line = QtCore.QLineF(start_orig, end_orig)
            pixel_length = line.length()
            self.original_pixel_length = pixel_length

            if pixel_length < 1.0:
                self._show_error_message("Scale Error", "Scale line is too short.")
                self.view.set_scale_line_points(None, None) # Clear the line in view
                self.original_pixel_length = None
                return

            # --- Calculate Scale Factor ---
            real_length_str = self.real_world_length_edit.text().replace(locale.localeconv()['decimal_point'], '.')
            real_length = float(real_length_str)
            if real_length <= 0: raise ValueError("Real-world length must be positive.")

            self.current_scale_factor = real_length / pixel_length
            self.scale_factor_label.setText(f"<b>Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            self.update_scale_reminder(is_set=True)
            logger.info(f"Scale factor calculated: {self.current_scale_factor:.4f} µm/pixel (Line: {pixel_length:.2f} px)")

            # Update manual scale edit field as well
            self.manual_scale_edit.setText(f"{self.current_scale_factor:.6f}")

            self.update_action_states()
            # Automatically switch back to selection mode?
            self.view.set_mode(CustomGraphicsView.MODE_SELECTION)
            if CustomGraphicsView.MODE_SELECTION in self.mode_buttons:
                 self.mode_buttons[CustomGraphicsView.MODE_SELECTION].setChecked(True)

        except ValueError as e:
            self._show_error_message("Input Error", f"Invalid input for scale calculation.\n{e}")
            self.view.set_scale_line_points(None, None) # Clear line
            self.current_scale_factor = None
            self.original_pixel_length = None
            self.update_scale_reminder()
        except Exception as e:
            logger.exception("Error calculating scale factor from line:")
            self._show_error_message("Scale Error", f"Error calculating scale: {e}")
            self.view.set_scale_line_points(None, None)
            self.current_scale_factor = None
            self.original_pixel_length = None
            self.update_scale_reminder()

    @Slot()
    def set_manual_scale(self):
        """Sets the scale factor manually from the line edit."""
        if self.uploaded_image is None: return

        try:
            scale_str = self.manual_scale_edit.text().replace(locale.localeconv()['decimal_point'], '.')
            scale_factor = float(scale_str)
            if scale_factor <= 0: raise ValueError("Scale factor must be positive.")

            self.current_scale_factor = scale_factor
            self.original_pixel_length = None # Manual scale doesn't have a drawn line length
            self.scale_factor_label.setText(f"<b>Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            self.update_scale_reminder(is_set=True)
            logger.info(f"Scale factor set manually: {self.current_scale_factor:.4f} µm/pixel")

            # Clear any previously drawn scale line state in the view
            self.view.set_scale_line_points(None, None)

            self.update_action_states()
        except ValueError as e:
            self._show_error_message("Input Error", f"Invalid scale factor value.\n{e}")
            self.current_scale_factor = None
            self.update_scale_reminder()
        except Exception as e:
            logger.exception("Error setting manual scale factor:")
            self._show_error_message("Scale Error", f"Unexpected error setting scale: {e}")
            self.current_scale_factor = None
            self.update_scale_reminder()

    @Slot()
    def reset_scale(self):
        """Clears the current scale factor and related UI elements."""
        if self.current_scale_factor is None: return

        reply = QMessageBox.question(
            self, "Confirm Reset Scale", "Clear current scale factor?",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
        )
        if reply == QMessageBox.Cancel: return

        logger.info("Resetting scale factor.")
        self.current_scale_factor = None
        self.original_pixel_length = None
        self.view.set_scale_line_points(None, None) # Clear points & line in view
        self.scale_factor_label.setText("<b>Current Scale: Not set</b>")
        self.manual_scale_edit.setText("0.0")
        self.update_scale_reminder()
        self.update_action_states()
        self.update_status("Scale reset.")

    def update_scale_reminder(self, is_set=None):
         """Updates the scale reminder label based on current state."""
         if is_set is None: is_set = self.current_scale_factor is not None
         label = self.scale_reminder_label

         if self.uploaded_image is None:
              label.setText("Load an image to start.")
              label.setStyleSheet("color: #FFA500; font-weight: bold; background-color: #404040; padding: 5px; border-radius: 3px;")
              label.show()
         elif not is_set:
              label.setText("Scale not set. Draw line or enter value.")
              label.setStyleSheet("color: #FF6347; font-weight: bold; background-color: #404040; padding: 5px; border-radius: 3px;")
              label.show()
         else:
              label.setText("Scale is set. Ready to process.")
              label.setStyleSheet("color: #90EE90; font-weight: bold; background-color: #305030; padding: 5px; border-radius: 3px;")
              # Optionally hide it after a delay
              QTimer.singleShot(3000, lambda: label.hide() if self.current_scale_factor is not None else None)


    @Slot()
    def start_processing(self):
        """Initiates the image processing in a background thread."""
        logger.info("Processing requested.")
        if self.model is None: return self._show_error_message("Model Error", "Model not loaded.")
        if self.uploaded_image is None: return self._show_error_message("Warning", "Upload image first.")
        if self.current_scale_factor is None: return self._show_error_message("Scale Error", "Set image scale first.")
        if self.processing_thread and self.processing_thread.isRunning(): return QMessageBox.warning(self, "Busy", "Processing already running.")

        # Get parameters from the currently visible parameter widget
        try:
             current_param_widget = self.model_params_stack.currentWidget()
             if isinstance(current_param_widget, (FastSAMParameterWidget, MobileSAMParameterWidget)):
                  params = current_param_widget.get_parameters()
                  model_type_to_run = params['model_type'] # Get the selected model type
                  logger.info(f"Processing requested for {model_type_to_run.upper()}.")
             else:
                  raise TypeError("Invalid parameter widget found.")
        except (ValueError, TypeError) as e:
            self._show_error_message("Parameter Error", f"Invalid parameters: {e}")
            return

        # --- Determine Target Device Based on Model Type ---
        target_device = None
        if model_type_to_run == 'fastsam':
            target_device = torch.device("cpu") # Force CPU for FastSAM
            logger.info("FastSAM selected: Forcing CPU processing.")
        elif model_type_to_run == 'mobilesam':
            # Prioritize GPU if available, otherwise use CPU
            if torch.cuda.is_available():
                target_device = torch.device("cuda")
                logger.info("MobileSAM selected: Using CUDA device.")
            else:
                target_device = torch.device("cpu")
                logger.info("MobileSAM selected: CUDA not available, using CPU.")
        else:
            # Fallback or error for unknown type - should not happen if params are valid
             logger.error(f"Unknown model type '{model_type_to_run}' during device selection.")
             self._show_error_message("Internal Error", f"Unknown model type '{model_type_to_run}'.")
             return

        if target_device is None: # Should already be handled, but for safety
            logger.error("Could not determine target device for processing.")
            self._show_error_message("Internal Error", "Could not set processing device.")
            return
        # --- End Device Selection Fix ---

        # --- Reset previous results ---
        self.annotations = None
        self.df = None
        self.processed_image_vis = None
        self.clear_scene_graphics_items(clear_pixmap=False) # Keep base image, clear overlays
        self.results_widget.clear()
        logger.debug("Cleared previous analysis results.")

        # --- Setup Worker and Thread ---
        self.processing_worker = ProcessingWorker(
            self.uploaded_image, # Pass original PIL image
            self.model,          # Pass loaded model object
            self.device,         # Pass target device
            self.current_scale_factor,
            params               # Pass collected parameters
        )
        self.processing_thread = QThread()
        self.processing_worker.moveToThread(self.processing_thread)

        # --- Connections ---
        self.processing_worker.finished.connect(self.on_processing_finished)
        self.processing_worker.error.connect(self.on_processing_error)
        self.processing_worker.progress.connect(self.update_progress)
        self.processing_worker.status.connect(self.update_status)
        self.processing_thread.started.connect(self.processing_worker.run)
        # Cleanup connections
        self.processing_worker.finished.connect(self.processing_thread.quit)
        self.processing_worker.finished.connect(self.processing_worker.deleteLater)
        self.processing_thread.finished.connect(self.processing_thread.deleteLater)
        self.processing_thread.finished.connect(self._on_thread_finished) # Clean up references

        # --- Start Thread ---
        self.processing_thread.start()
        self.update_action_states(processing=True)
        self.update_status("Processing started...")
        self.update_progress(1)

    def stop_processing_thread(self):
        """Requests the processing thread to stop."""
        if self.processing_thread and self.processing_thread.isRunning():
            logger.info("Attempting to stop processing thread.")
            if self.processing_worker:
                 self.processing_worker.stop() # Signal worker to stop
            # Give worker a chance to finish, then quit thread
            # Note: Direct quit/terminate can be risky if worker holds resources
            # Rely on worker checking its _is_running flag
            QTimer.singleShot(100, self.processing_thread.quit) # Ask thread to quit after worker signal
            # self.processing_thread.wait(500) # Optional short wait

    @Slot(object, object, object)
    def on_processing_finished(self, df_result, annotations_result, segmented_image_vis_result):
        """Handles results from the ProcessingWorker."""
        logger.info("Processing finished signal received.")
        self.update_progress(0)

        if df_result is not None and annotations_result is not None and segmented_image_vis_result is not None:
            self.df = df_result
            self.annotations = annotations_result # Store final valid annotations
            self.processed_image_vis = segmented_image_vis_result

            logger.info(f"Processing successful. Received {len(self.df)} results.")

            # Update UI: Display visualization and populate results
            self.display_image_on_scene(self.processed_image_vis) # Show contours-only vis
            self.draw_grain_highlights() # Draw interactive overlays
            self.results_widget.populate(self.df) # Populate results table
            self.update_status(f"Processing complete: {len(self.df)} grains found.")
        else:
             logger.warning("Processing finished but results are None/incomplete.")
             self.update_status("Processing finished, but no valid results generated.")
             # Display original image if visualization missing
             if self.uploaded_image and self.pixmap_item is None:
                  self.display_image_on_scene(self.uploaded_image)

        # Explicitly update action states to ensure buttons are re-enabled
        # This is a safeguard in case the thread's finished signal doesn't trigger properly
        QTimer.singleShot(100, lambda: self.update_action_states(processing=False))

        # self.update_action_states(processing=False) # _on_thread_finished handles this

    @Slot(str)
    def on_processing_error(self, error_message):
        """Handles errors reported by the ProcessingWorker."""
        logger.error(f"Processing error signal received: {error_message}")
        self.update_progress(0)
        self._show_error_message("Processing Error", error_message)
        self.update_status(f"Processing failed: {error_message}")
        # Display original image if processing failed visually
        if self.uploaded_image and self.pixmap_item is None:
            self.display_image_on_scene(self.uploaded_image)

        # Explicitly update action states to ensure buttons are re-enabled
        # This is a safeguard in case the thread's finished signal doesn't trigger properly
        QTimer.singleShot(100, lambda: self.update_action_states(processing=False))

    @Slot()
    def _on_thread_finished(self):
         """Cleans up thread-related variables when the QThread finishes."""
         logger.debug("Processing QThread finished signal received.")
         self.processing_thread = None
         self.processing_worker = None
         self.update_action_states(processing=False) # Ensure UI state is correct

    @Slot()
    def _on_patch_thread_finished(self):
         """Cleans up patch processing thread-related variables when the QThread finishes."""
         logger.debug("Patch processing QThread finished signal received.")
         self.patch_processing_thread = None
         self.patch_processing_worker = None
         self.update_action_states(processing=False) # Ensure UI state is correct


    # --- Visualization and Interaction ---

    def draw_grain_highlights(self):
        """Draws interactive polygons/text for valid grains on the scene."""
        if self.df is None or self.annotations is None: return
        # Check if lengths match BEFORE proceeding
        num_df_rows = len(self.df)
        num_annotations = len(self.annotations) if isinstance(self.annotations, list) else (self.annotations.shape[0] if isinstance(self.annotations, torch.Tensor) else 0)

        if num_df_rows != num_annotations:
             logger.error(f"Cannot draw highlights: Mismatch between DataFrame ({num_df_rows}) and annotations ({num_annotations}).")
             # Clear existing highlights if state is inconsistent
             self.clear_scene_graphics_items(clear_pixmap=False)
             return

        if self.pixmap_item is None: return # No base image
        if num_df_rows == 0: # No valid grains to draw
            self.clear_scene_graphics_items(clear_pixmap=False) # Clear any old highlights
            return


        logger.info(f"Drawing interactive highlights for {num_df_rows} grains.")
        self.clear_scene_graphics_items(clear_pixmap=False) # Clear previous highlights

        # Scaling Logic (map original annotation coords to displayed pixmap coords)
        try:
            display_width = self.pixmap_item.pixmap().width()
            display_height = self.pixmap_item.pixmap().height()
            original_width = self.uploaded_image.width # Assumes uploaded_image exists
            original_height = self.uploaded_image.height
            if display_width == 0 or original_width == 0: raise ValueError("Zero image dimensions")
            scale_x = display_width / original_width
            scale_y = display_height / original_height
        except Exception as e:
            logger.error(f"Error getting dimensions for highlight scaling: {e}")
            return

        # Define Pens (should match ResultsViewWidget selection style if possible)
        default_pen = QPen(QColor(0, 255, 255, 200), 1.5) # Cyan outline
        default_pen.setCosmetic(True)
        selected_pen = QPen(QColor(255, 0, 0, 255), 2.5) # Red outline for selection
        selected_pen.setCosmetic(True)

        # Font Settings
        base_font_size = 8
        min_font_size = 5
        max_font_size = 16

        current_selection = self.results_widget.selected_df_indices # Get selection from results view

        # Iterate through DataFrame and corresponding Annotations
        # Assuming self.annotations is ordered consistently with self.df.index
        annotation_list = self.annotations if isinstance(self.annotations, list) else [self.annotations[i] for i in range(num_annotations)]

        for df_index, mask_tensor in zip(self.df.index, annotation_list):
            try:
                mask_np = mask_tensor.cpu().numpy().astype(np.uint8)
                contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                if not contours: continue
                contour = max(contours, key=cv2.contourArea)

                # Scale contour to display coordinates
                scaled_contour = contour.astype(np.float32)
                scaled_contour[:, :, 0] *= scale_x
                scaled_contour[:, :, 1] *= scale_y
                scaled_contour = scaled_contour.astype(np.int32)

                qpolygon = QtGui.QPolygonF()
                for point in scaled_contour.squeeze():
                    if len(point) == 2: qpolygon.append(QPointF(point[0], point[1]))

                if qpolygon.count() < 3: continue

                # Create Polygon Item
                polygon_item = QGraphicsPolygonItem(qpolygon)
                polygon_item.setFlag(QGraphicsPolygonItem.ItemIsSelectable, False) # Selection handled via click mapping
                polygon_item.setAcceptHoverEvents(True) # Keep hover if needed for tooltips
                polygon_item.setData(Qt.UserRole, df_index) # Store DataFrame index

                # Style based on selection state from ResultsViewWidget
                is_selected = df_index in current_selection
                polygon_item.setPen(selected_pen if is_selected else default_pen)
                polygon_item.setBrush(Qt.NoBrush) # No fill
                polygon_item.setZValue(1 if is_selected else 0) # Bring selected forward

                self.scene.addItem(polygon_item)

                # Create Text Item (Object ID)
                text_id = f"{df_index}" # Use df index as ID label
                text_item = QGraphicsTextItem(text_id)
                text_item.setDefaultTextColor(QColor("white") if is_selected else QColor(255, 100, 100, 220))

                # Simple font size, or calculate based on bounds
                font = QFont("Arial", base_font_size)
                font.setBold(is_selected)  # Make selected text bold
                text_item.setFont(font)

                # Position text at centroid (calculated on scaled contour)
                M = cv2.moments(scaled_contour)
                cX, cY = 0, 0
                if M["m00"] != 0: cX, cY = int(M["m10"]/M["m00"]), int(M["m01"]/M["m00"])
                else: cX, cY = int(qpolygon.boundingRect().center().x()), int(qpolygon.boundingRect().center().y())

                text_rect = text_item.boundingRect()
                text_item.setPos(cX - text_rect.width() / 2, cY - text_rect.height() / 2)
                text_item.setFlag(QGraphicsTextItem.ItemIgnoresTransformations, False) # Scale with view
                text_item.setData(Qt.UserRole, df_index)
                text_item.setZValue(polygon_item.zValue() + 0.1) # Ensure text is above polygon

                self.scene.addItem(text_item)

                # Store references
                self.grain_items[df_index] = {'poly': polygon_item, 'text': text_item}

            except Exception as e:
                logger.exception(f"Error drawing highlight for grain index {df_index}: {e}")

        logger.info(f"Finished drawing {len(self.grain_items)} highlights.")
        self.scene.update()


    @Slot(QPointF)
    def on_scene_clicked(self, scene_pos: QPointF):
        """Handles clicks on the graphics scene to select grains."""
        if self.view.mode != CustomGraphicsView.MODE_SELECTION: return
        if not self.grain_items: return

        logger.debug(f"Scene clicked at: {scene_pos.x():.1f}, {scene_pos.y():.1f}")

        items_at_pos = self.scene.items(scene_pos)
        candidate_grains = [] # (area, df_index, item)

        for item in items_at_pos:
            if isinstance(item, QGraphicsPolygonItem):
                df_index = item.data(Qt.UserRole)
                if df_index is not None and df_index in self.grain_items and item.contains(scene_pos):
                    # Use bounding box area for quick sorting, smaller is "more inside"
                    area = item.boundingRect().width() * item.boundingRect().height()
                    candidate_grains.append((area, df_index, item))

        clicked_grain_index = None
        if candidate_grains:
            candidate_grains.sort(key=lambda x: x[0]) # Sort by area ascending
            _, clicked_grain_index, _ = candidate_grains[0]
            logger.debug(f"Clicked on grain index: {clicked_grain_index}")
        else:
            logger.debug("Clicked on empty area.")

        # Handle selection logic (Ctrl for multi-select)
        modifiers = QApplication.keyboardModifiers()
        is_multi_select = modifiers == Qt.ControlModifier
        current_selection = self.results_widget.selected_df_indices.copy() # Get current selection
        new_selection = set()

        if clicked_grain_index is not None:
            if is_multi_select:
                if clicked_grain_index in current_selection:
                    current_selection.remove(clicked_grain_index) # Toggle off
                else:
                    current_selection.add(clicked_grain_index) # Toggle on
                new_selection = current_selection
            else:
                # Single select: if already selected, deselect; otherwise select only this one
                if clicked_grain_index in current_selection and len(current_selection) == 1:
                    new_selection = set() # Deselect
                else:
                    new_selection = {clicked_grain_index} # Select only this
        else:
            # Clicked outside
            if not is_multi_select:
                new_selection = set() # Clear selection
            else:
                new_selection = current_selection # Keep selection in multi-mode

        # --- FIX: Update both TreeView and Canvas Highlights ---
        # 1. Update the results view selection (this updates the tree visually)
        #    set_selected_indices internally handles not re-emitting if unchanged
        self.results_widget.set_selected_indices(new_selection)

        # 2. *Explicitly* update the canvas highlights based on the new selection set
        #    We pass the final 'new_selection' set here.
        self.update_grain_visual_selection(new_selection)
        # Explicitly update button states after click interaction
        self.update_action_states()
        # --- End Fix ---

    @Slot(set)
    def on_results_view_selection_changed(self, selected_indices: set):
        """Updates highlights when selection changes in the results view."""
        logger.debug(f"Syncing highlights for selection: {selected_indices}")
        # Redraw highlights is efficient enough for moderate numbers
        # For very large numbers, updating only changed items might be better
        self.update_grain_visual_selection(selected_indices)
        # Explicitly update button states after tree selection
        self.update_action_states()

    def update_grain_visual_selection(self, selected_indices: set):
         """Updates the visual style of grain items based on the provided selection set."""
         if not self.grain_items: return

         # Define Pens again (or access them if stored)
         default_pen = QPen(QColor(0, 255, 255, 200), 1.5); default_pen.setCosmetic(True)
         selected_pen = QPen(QColor(255, 0, 0, 255), 2.5); selected_pen.setCosmetic(True)
         default_text_color = QColor(150, 50, 50, 220) # Darker semi-transparent reddish
         selected_text_color = QColor("black") # Black for contrast

         # Font Sizes and Styles
         DEFAULT_TEXT_SIZE = 14  # Original base size used in draw_grain_highlights
         SELECTED_TEXT_SIZE = 14 # Make selected text slightly larger

         for df_index, items in self.grain_items.items():
             poly_item = items.get('poly')
             text_item = items.get('text')
             if not poly_item: continue

             is_selected = df_index in selected_indices

             # Update Polygon
             poly_item.setPen(selected_pen if is_selected else default_pen)
             poly_item.setZValue(1 if is_selected else 0)

             # Update Text
             if text_item:
                 # Get the current font to modify it
                 current_font = text_item.font()

                 if is_selected:
                     text_item.setDefaultTextColor(selected_text_color)
                     current_font.setPointSize(SELECTED_TEXT_SIZE)
                     current_font.setBold(True)
                 else:
                     text_item.setDefaultTextColor(default_text_color)
                     current_font.setPointSize(DEFAULT_TEXT_SIZE)
                     current_font.setBold(False)

                 # Apply the modified font back to the item
                 text_item.setFont(current_font)
                 text_item.setZValue(poly_item.zValue() + 0.1)

         self.scene.update() # Request repaint


    # --- Deletion and Recalculation ---

    @Slot(set) # Connected to ResultsViewWidget.delete_requested
    def delete_selected_grains(self, indices_to_delete: set):
        """Deletes selected grains from data and updates visualization."""
        if not indices_to_delete: return # Should be handled by button state
        if self.df is None or self.annotations is None or self.uploaded_image is None:
             return self._show_error_message("Deletion Error", "Cannot delete, data missing.")

        num_to_delete = len(indices_to_delete)
        reply = QMessageBox.question(
            self, "Confirm Deletion", f"Delete {num_to_delete} selected grain(s)?",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
        )
        if reply == QMessageBox.Cancel: return

        logger.info(f"Deleting {num_to_delete} grains: {indices_to_delete}")
        QApplication.setOverrideCursor(Qt.WaitCursor)

        try:
            # --- Filter Data ---
            # Ensure indices actually exist in the current DataFrame
            valid_indices_to_delete = indices_to_delete.intersection(set(self.df.index))
            if not valid_indices_to_delete:
                 logger.warning("None of the selected indices found in current DataFrame.")
                 QApplication.restoreOverrideCursor()
                 return

            # Create a mask for rows to keep in the DataFrame
            df_keep_mask = ~self.df.index.isin(valid_indices_to_delete)
            original_df_indices = self.df.index.tolist() # Get original order

            # --- Filter Annotations ---
            # Map df indices to indices within the annotations list/tensor
            # This assumes self.annotations corresponds *exactly* in order to self.df.index at this point
            if len(self.df) != (len(self.annotations) if isinstance(self.annotations, list) else (self.annotations.shape[0] if isinstance(self.annotations, torch.Tensor) else -1)):
                 raise RuntimeError("CRITICAL: Mismatch between DataFrame and Annotations before deletion.")

            indices_to_keep_in_annotations = [
                 i for i, df_idx in enumerate(original_df_indices) if df_idx not in valid_indices_to_delete
            ]

            # Filter the annotations structure
            if isinstance(self.annotations, torch.Tensor):
                if indices_to_keep_in_annotations:
                     keep_tensor = torch.tensor(indices_to_keep_in_annotations, dtype=torch.long, device=self.annotations.device)
                     self.annotations = self.annotations[keep_tensor]
                else: # All deleted
                     self.annotations = torch.empty((0,) + self.annotations.shape[1:], dtype=self.annotations.dtype, device=self.annotations.device)
            elif isinstance(self.annotations, list):
                 self.annotations = [self.annotations[i] for i in indices_to_keep_in_annotations]

            # --- Filter DataFrame ---
            self.df = self.df[df_keep_mask]

            # --- Update UI ---
            self.update_status(f"Deleted {len(valid_indices_to_delete)} grains. Updating visualization...")
            QApplication.processEvents()

            # Regenerate contour visualization using the *filtered* annotations
            # Get current contour thickness from appropriate param widget
            current_param_widget = self.model_params_stack.currentWidget()
            contour_thickness = DEFAULT_CONTOUR_THICKNESS # Fallback
            if hasattr(current_param_widget, 'contour_thickness_slider'):
                 contour_thickness = current_param_widget.contour_thickness_slider.value()

            # Use core.image_utils (assuming it's imported)
            # This needs the *original* uploaded image
            new_vis = create_segmented_visualization(
                 self.uploaded_image,
                 self.annotations, # Pass filtered annotations
                 contour_thickness=contour_thickness
            )

            if new_vis:
                 self.processed_image_vis = new_vis
                 self.display_image_on_scene(self.processed_image_vis)
            else:
                 logger.error("Failed to regenerate visualization after deletion. Displaying base image.")
                 self.display_image_on_scene(self.uploaded_image) # Fallback

            # Redraw highlights based on the *now filtered* self.df and self.annotations
            self.draw_grain_highlights()
            # Repopulate the results table with filtered data
            self.results_widget.populate(self.df)

            self.update_status(f"Deleted {len(valid_indices_to_delete)} grains. Visualization updated.")
            logger.info("Deletion complete.")

        except Exception as e:
            logger.exception("Error during grain deletion process:")
            self._show_error_message("Deletion Error", f"Error deleting grains: {e}")
            self.update_status("Error during deletion.")
            # Consider advising user to reset or reload if state might be corrupt
        finally:
            QApplication.restoreOverrideCursor()
            self.update_action_states() # Update button states


    @Slot()
    def recalculate_parameters_with_new_scale(self):
        """Recalculates parameters using existing annotations and current scale."""
        logger.info("Recalculate Parameters requested.")
        if self.processing_thread and self.processing_thread.isRunning(): return QMessageBox.warning(self, "Busy", "Cannot recalculate while processing.")
        if self.annotations is None: return self._show_error_message("No Segmentation", "No segmentation results exist.")
        if self.current_scale_factor is None: return self._show_error_message("No Scale", "Set a valid scale factor.")

        num_annotations = len(self.annotations) if isinstance(self.annotations, list) else (self.annotations.shape[0] if isinstance(self.annotations, torch.Tensor) else 0)
        if num_annotations == 0: return self._show_error_message("No Annotations", "No annotations exist to recalculate.")


        reply = QMessageBox.question(
            self, "Confirm Recalculation",
            f"Recalculate parameters for {num_annotations} annotations using scale {self.current_scale_factor:.4f} µm/pixel?\nThis overwrites existing parameters.",
            QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Yes
        )
        if reply == QMessageBox.Cancel: return

        logger.info(f"Starting recalculation with scale: {self.current_scale_factor:.4f}")
        self.update_status("Recalculating parameters...")
        QApplication.setOverrideCursor(Qt.WaitCursor)
        self.update_progress(1) # Show progress bar start

        try:
            # --- Perform Recalculation (directly, assuming it's fast enough) ---
            # If slow, should use the worker thread pattern again.
            # Need to adapt calculate_parameters to accept a simple callable for progress.
            progress_adapter = lambda val: QMetaObject.invokeMethod(self, "update_progress", Qt.QueuedConnection, Q_ARG(int, val))

            # Use core.analysis function
            new_df, valid_mask = calculate_parameters(
                self.annotations,
                self.current_scale_factor,
                progress_callback=progress_adapter
            )

            # Filter annotations based on the *new* validity mask
            # This ensures df and annotations stay synchronized if some items become invalid
            # with the new scale (e.g., area becomes too small).
            if np.sum(valid_mask) != num_annotations:
                logger.warning(f"Number of valid annotations changed during recalculation ({np.sum(valid_mask)} / {num_annotations}). Filtering annotations.")
                indices_to_keep = np.where(valid_mask)[0]

                if isinstance(self.annotations, torch.Tensor):
                    if indices_to_keep.size > 0:
                         keep_tensor = torch.from_numpy(indices_to_keep).long().to(self.annotations.device)
                         self.annotations = self.annotations[keep_tensor]
                    else:
                         self.annotations = torch.empty((0,) + self.annotations.shape[1:], dtype=self.annotations.dtype, device=self.annotations.device)
                elif isinstance(self.annotations, list):
                     self.annotations = [self.annotations[i] for i in indices_to_keep]
            else:
                 logger.info("All existing annotations remained valid after recalculation.")


            # Update the main DataFrame
            self.df = new_df

            # --- Update UI ---
            self.results_widget.populate(self.df) # Refresh table
            self.draw_grain_highlights() # Redraw highlights based on new df/annotations
            self.update_status(f"Parameters recalculated: {len(self.df)} valid objects.")

        except Exception as e:
            logger.exception("Error during parameter recalculation:")
            self._show_error_message("Recalculation Error", f"Error recalculating: {e}")
            self.update_status("Recalculation failed.")
        finally:
            QApplication.restoreOverrideCursor()
            self.update_progress(0) # Hide progress bar
            self.update_action_states()


    # --- Saving Methods ---

    @Slot()
    def save_results(self):
        """Saves DataFrame CSV and annotated image PNG."""
        if self.df is None or self.df.empty: return self._show_error_message("No Results", "No results to save.")

        save_image = True
        if self.processed_image_vis is None:
             logger.warning("DataFrame exists, but processed visualization image is missing.")
             reply = QMessageBox.question(self, "Missing Image", "Annotated image missing. Save only data (CSV)?", QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)
             if reply == QMessageBox.No: return
             save_image = False

        base_filename = os.path.splitext(self.image_filename or "analysis")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_csv = f"{base_filename}_results_{timestamp}.csv"
        suggested_img = f"{base_filename}_segmented_{timestamp}.png"

        target_dir = self.default_save_dir or self.last_save_dir
        save_dir = QFileDialog.getExistingDirectory(self, "Select Directory to Save", target_dir)
        if not save_dir: return
        self.last_save_dir = save_dir

        saved_files = []
        errors = []
        try:
            # Save CSV
            df_file_path = os.path.join(save_dir, suggested_csv)
            decimal_sep = locale.localeconv().get('decimal_point', '.')
            self.df.to_csv(df_file_path, index=False, decimal=decimal_sep)
            saved_files.append(f"Data: {suggested_csv}")
            logger.info(f"Results saved to: {df_file_path}")

            # Save Image
            if save_image and self.processed_image_vis:
                img_file_path = os.path.join(save_dir, suggested_img)
                self.processed_image_vis.save(img_file_path, "PNG")
                saved_files.append(f"Image: {suggested_img}")
                logger.info(f"Segmented image saved to: {img_file_path}")
        except Exception as e:
            logger.exception("Failed to save results.")
            errors.append(f"Error saving: {e}")

        # Report Outcome
        if saved_files:
            msg = f"Results saved in:\n{save_dir}\n\n" + "\n".join(saved_files)
            if errors: msg += "\n\nErrors:\n" + "\n".join(errors)
            QMessageBox.information(self, "Save Successful", msg)
        else:
             self._show_error_message("Save Error", "Failed to save results.\n" + "\n".join(errors))

    @Slot()
    def save_current_view(self):
        """Saves the current QGraphicsView content."""
        if not self.scene or not self.scene.items(): return self._show_error_message("Nothing to Save", "View is empty.")

        base_filename = os.path.splitext(self.image_filename or "view")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_filename = f"{base_filename}_view_{timestamp}.png"
        target_dir = self.default_save_dir or self.last_save_dir

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Current View As", os.path.join(target_dir, suggested_filename),
            "PNG Image (*.png);;JPEG Image (*.jpg *.jpeg);;All Files (*)"
        )
        if not file_path: return
        self.last_save_dir = os.path.dirname(file_path)

        try:
            rect = self.scene.itemsBoundingRect()
            image = QImage(rect.size().toSize(), QImage.Format_ARGB32_Premultiplied)
            image.fill(Qt.transparent)
            painter = QPainter(image)
            # Match background brush from view for non-transparent saving?
            # painter.fillRect(image.rect(), self.view.backgroundBrush())
            painter.setRenderHint(QPainter.Antialiasing)
            painter.setRenderHint(QPainter.SmoothPixmapTransform)
            self.scene.render(painter, QRectF(image.rect()), rect)
            painter.end()
            if not image.save(file_path): raise IOError("Failed to save image.")
            QMessageBox.information(self, "View Saved", f"View saved to:\n{file_path}")
            logger.info(f"Current view saved to: {file_path}")
        except Exception as e:
            logger.exception("Failed to save current view.")
            self._show_error_message("Save Error", f"Failed to save view: {e}")

    @Slot()
    def save_scale_factor(self):
        """Saves current scale factor settings to JSON."""
        if self.current_scale_factor is None: return self._show_error_message("No Scale", "No scale factor set.")

        target_dir = self.default_save_dir or self.last_save_dir
        suggested_filename = f"{os.path.splitext(self.image_filename or 'scale')[0]}_scale.json"
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save Scale Factor", os.path.join(target_dir, suggested_filename), "JSON files (*.json)"
        )
        if not file_path: return
        self.last_save_dir = os.path.dirname(file_path)

        # Get real-world length if available
        try:
             rwl = float(self.real_world_length_edit.text().replace(locale.localeconv()['decimal_point'], '.'))
        except:
             rwl = None

        scale_data = {
            'scale_factor_um_per_pixel': self.current_scale_factor,
            'real_world_length_um': rwl,
            'original_image_pixel_length': self.original_pixel_length,
            'source_image': self.image_file_path,
            'timestamp': datetime.now().isoformat()
        }
        try:
            with open(file_path, 'w') as f: json.dump(scale_data, f, indent=4)
            self.update_status(f"Scale factor saved to {os.path.basename(file_path)}")
            QMessageBox.information(self, "Scale Saved", f"Scale saved to:\n{file_path}")
        except Exception as e:
            logger.exception(f"Failed to save scale factor to {file_path}")
            self._show_error_message("Save Error", f"Failed to save scale factor: {e}")

    @Slot()
    def load_scale_factor(self):
        """Loads scale factor settings from JSON."""
        if self.uploaded_image is None: return self._show_error_message("Load Image First", "Load image before loading scale.")

        target_dir = self.default_save_dir or self.last_save_dir
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Scale Factor", target_dir, "JSON files (*.json)")
        if not file_path: return
        self.last_save_dir = os.path.dirname(file_path)

        try:
            with open(file_path, 'r') as f: scale_data = json.load(f)

            loaded_scale = scale_data.get('scale_factor_um_per_pixel')
            if loaded_scale is None or loaded_scale <= 0: raise ValueError("Invalid scale factor in file.")

            self.current_scale_factor = loaded_scale
            self.original_pixel_length = scale_data.get('original_image_pixel_length')
            rwl = scale_data.get('real_world_length_um')

            # Update UI
            self.scale_factor_label.setText(f"<b>Scale: {self.current_scale_factor:.4f} µm/pixel</b>")
            if rwl: self.real_world_length_edit.setText(locale.format_string("%.2f", rwl))
            self.manual_scale_edit.setText(locale.format_string("%.6f", self.current_scale_factor))
            self.update_scale_reminder(is_set=True)

            # Update view's scale line points (if loaded)
            # Assuming scale line points weren't saved/loaded, clear any existing one
            self.view.set_scale_line_points(None, None)

            self.update_action_states()
            self.update_status(f"Scale factor loaded from {os.path.basename(file_path)}")
            QMessageBox.information(self, "Scale Loaded", f"Scale factor loaded: {self.current_scale_factor:.4f} µm/pixel")

        except (FileNotFoundError, json.JSONDecodeError, KeyError, ValueError) as e:
             logger.error(f"Failed to load scale file {file_path}: {e}")
             self._show_error_message("Load Error", f"Failed to load scale factor file.\nError: {e}")
             self.current_scale_factor = None
             self.update_scale_reminder()
        except Exception as e:
            logger.exception(f"Unexpected error loading scale factor from {file_path}")
            self._show_error_message("Load Error", f"An unexpected error occurred: {e}")
            self.current_scale_factor = None
            self.update_scale_reminder()


    @Slot()
    def save_coco_annotations(self):
        """Saves current annotations in COCO JSON format."""
        if self.annotations is None: return self._show_error_message("No Annotations", "No annotations to save.")
        if self.uploaded_image is None: return self._show_error_message("No Image", "Original image needed for dimensions.")

        num_annotations = len(self.annotations) if isinstance(self.annotations, list) else (self.annotations.shape[0] if isinstance(self.annotations, torch.Tensor) else 0)
        if num_annotations == 0: return self._show_error_message("No Annotations", "Annotation list is empty.")

        # Optional: Check for df/annotation mismatch (though generate_coco_dict uses annotations directly)
        # if self.df is not None and len(self.df) != num_annotations:
        #     logger.warning("DataFrame size doesn't match annotation count for COCO export.")

        base_filename = os.path.splitext(self.image_filename or "coco_export")[0]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        suggested_filename = f"{base_filename}_coco_{timestamp}.json"
        target_dir = self.default_save_dir or self.last_save_dir

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Save COCO Annotations", os.path.join(target_dir, suggested_filename), "JSON files (*.json)"
        )
        if not file_path: return
        self.last_save_dir = os.path.dirname(file_path)

        try:
            # Generate COCO dictionary using core.io function
            coco_dict = generate_coco_dict(
                self.annotations,
                self.uploaded_image.height,
                self.uploaded_image.width,
                self.image_filename
            )

            if coco_dict is None:
                 raise ValueError("Failed to generate COCO dictionary (check logs).")

            # Save the dictionary to file
            with open(file_path, 'w') as f:
                json.dump(coco_dict, f, indent=2)

            QMessageBox.information(self, "Export Successful", f"COCO annotations saved to:\n{file_path}")
            logger.info(f"COCO annotations saved to {file_path}")

        except Exception as e:
            logger.exception("Failed to save COCO annotations.")
            self._show_error_message("Export Error", f"Failed to save COCO annotations: {e}")

    @Slot()
    def set_default_save_directory(self):
        """Allows user to set a persistent default save directory."""
        directory = QFileDialog.getExistingDirectory(
             self, "Select Default Save Directory", self.default_save_dir or self.last_save_dir
        )
        if directory:
            self.default_save_dir = directory
            self.update_status(f"Default save directory set: {directory}")
            logger.info(f"Default save directory set: {directory}")
            # TODO: Persist this setting (e.g., in theme_config.json or separate file)


    # --- Other UI Slots & Methods ---

    @Slot()
    def reset_view_zoom(self):
         """Resets the view's zoom and centers the image."""
         self.view.reset_view() # View handles fitting if items exist


    @Slot()
    def show_plotting_dialog(self):
        """Shows the dialog to select parameters for plotting."""
        if self.df is None or self.df.empty: return self._show_error_message("No Data", "No results to plot.")
        # Use the dialog function from gui.dialogs
        plot_dialog = show_plot_selection_dialog(self.df, self)
        if plot_dialog:
             # Need to store reference to prevent garbage collection if modeless
             self.plot_dialogs.append(plot_dialog)
             plot_dialog.finished.connect(lambda result, d=plot_dialog: self._remove_plot_dialog(d))
             plot_dialog.show()

    def _remove_plot_dialog(self, dialog):
         """Removes closed plot dialog from tracking list."""
         try:
             self.plot_dialogs.remove(dialog)
         except ValueError:
             pass # Already removed


    @Slot()
    def crop_image(self):
        """Opens the cropping dialog."""
        if self.uploaded_image is None: return self._show_error_message("No Image", "Load image first.")
        if self.processing_thread and self.processing_thread.isRunning(): return QMessageBox.warning(self, "Busy", "Cannot crop during processing.")

        # Use the dialog function from gui.dialogs
        # This function needs access to self.uploaded_image and should potentially
        # return the cropped image or update self.uploaded_image directly upon success.
        cropped_image = show_crop_dialog(self.uploaded_image, self)

        if cropped_image:
            logger.info("Applying cropped image.")
            # Reset state and update display with the new cropped image
            self.reset_app_state(clear_image=True) # Full reset
            self.uploaded_image = cropped_image
            # Update filename (optional)
            if self.image_filename:
                base, ext = os.path.splitext(self.image_filename)
                self.image_filename = f"{base}_cropped{ext}"

            self.display_image_on_scene(self.uploaded_image)
            self.update_action_states()
            self.update_scale_reminder() # Scale needs resetting
            self.update_status("Image cropped.")
            self.setWindowTitle(f"GrainSight - {self.image_filename}")


    @Slot()
    def analyze_with_patches(self):
        """Initiates patch-based segmentation using a dialog for configuration."""
        logger.info("Patch segmentation requested.")
        if self.model is None: return self._show_error_message("Model Error", "Model not loaded.")
        if self.uploaded_image is None: return self._show_error_message("Warning", "Upload image first.")
        if self.current_scale_factor is None: return self._show_error_message("Scale Error", "Set image scale first.")
        if self.patch_processing_thread and self.patch_processing_thread.isRunning():
            return QMessageBox.warning(self, "Busy", "Patch processing already running.")

        # Get patch configuration from dialog
        patch_config = show_patch_config_dialog(
            self.uploaded_image.width,
            self.uploaded_image.height,
            self
        )

        if not patch_config:
            logger.info("Patch configuration canceled by user.")
            return

        # Get parameters from the currently visible parameter widget
        try:
            current_param_widget = self.model_params_stack.currentWidget()
            if isinstance(current_param_widget, (FastSAMParameterWidget, MobileSAMParameterWidget)):
                params = current_param_widget.get_parameters()
                model_type_to_run = params['model_type'] # Get the selected model type
                logger.info(f"Patch processing requested for {model_type_to_run.upper()}.")
            else:
                raise TypeError("Invalid parameter widget found.")
        except (ValueError, TypeError) as e:
            self._show_error_message("Parameter Error", f"Invalid parameters: {e}")
            return

        # --- Determine Target Device Based on Model Type ---
        target_device = None
        if model_type_to_run == 'fastsam':
            target_device = torch.device("cpu") # Force CPU for FastSAM
            logger.info("FastSAM selected: Forcing CPU processing.")
        elif model_type_to_run == 'mobilesam':
            # Prioritize GPU if available, otherwise use CPU
            if torch.cuda.is_available():
                target_device = torch.device("cuda")
                logger.info("MobileSAM selected: Using CUDA device.")
            else:
                target_device = torch.device("cpu")
                logger.info("MobileSAM selected: CUDA not available, using CPU.")
        else:
            # Fallback or error for unknown type - should not happen if params are valid
            logger.error(f"Unknown model type '{model_type_to_run}' during device selection.")
            self._show_error_message("Internal Error", f"Unknown model type '{model_type_to_run}'.")
            return

        if target_device is None: # Should already be handled, but for safety
            logger.error("Could not determine target device for processing.")
            self._show_error_message("Internal Error", "Could not set processing device.")
            return
        # --- End Device Selection Fix ---

        # --- Reset previous results ---
        self.annotations = None
        self.df = None
        self.processed_image_vis = None
        self.clear_scene_graphics_items(clear_pixmap=False) # Keep base image, clear overlays
        self.results_widget.clear()
        logger.debug("Cleared previous analysis results.")

        # --- Setup Worker and Thread ---
        self.patch_processing_worker = PatchProcessingWorker(
            self.uploaded_image, # Pass original PIL image
            self.model,          # Pass loaded model object
            self.device,         # Pass target device
            self.current_scale_factor,
            params,              # Pass collected parameters
            patch_config         # Pass patch configuration
        )
        self.patch_processing_thread = QThread()
        self.patch_processing_worker.moveToThread(self.patch_processing_thread)

        # --- Connections ---
        self.patch_processing_worker.finished.connect(self.on_processing_finished) # Reuse same handler
        self.patch_processing_worker.error.connect(self.on_processing_error)       # Reuse same handler
        self.patch_processing_worker.progress.connect(self.update_progress)
        self.patch_processing_worker.status.connect(self.update_status)
        self.patch_processing_thread.started.connect(self.patch_processing_worker.run)
        # Cleanup connections
        self.patch_processing_worker.finished.connect(self.patch_processing_thread.quit)
        self.patch_processing_worker.finished.connect(self.patch_processing_worker.deleteLater)
        self.patch_processing_thread.finished.connect(self.patch_processing_thread.deleteLater)
        self.patch_processing_thread.finished.connect(self._on_patch_thread_finished) # Clean up references

        # --- Start Thread ---
        self.patch_processing_thread.start()
        self.update_action_states(processing=True)
        self.update_status("Starting patch-based segmentation...")
        logger.info(f"Patch processing started with config: {patch_config}")


    @Slot(int)
    def update_progress(self, value: int):
        """Updates the progress bar in the status bar."""
        if 0 < value < 100:
            self.status_progress.show()
            self.status_progress.setValue(value)
        else:
            self.status_progress.hide()
            self.status_progress.setValue(0)

    @Slot(str)
    def update_status(self, message: str):
        """Updates the status bar message."""
        self.status_bar.showMessage(message, 5000) # Show for 5 seconds
        logger.info(f"Status: {message}")
        # Consider if action states need update based on status message
        # self.update_action_states() # Can be too frequent

    @Slot() # Slot for safety if called via invokeMethod
    def update_action_states(self, processing: Optional[bool] = None):
        """Enable/disable actions and controls based on application state."""
        # Determine current state
        image_loaded = self.uploaded_image is not None
        # Check annotations exist (handle different types)
        num_annotations = 0
        if self.annotations is not None:
             if isinstance(self.annotations, list): num_annotations = len(self.annotations)
             elif isinstance(self.annotations, torch.Tensor): num_annotations = self.annotations.shape[0]
        annotations_exist = num_annotations > 0

        results_exist = self.df is not None and not self.df.empty
        scale_is_set = self.current_scale_factor is not None
        model_is_loaded = self.model is not None

        if processing is None:
            processing = (self.processing_thread is not None and self.processing_thread.isRunning()) or \
                         (self.patch_processing_thread is not None and self.patch_processing_thread.isRunning())
        elif processing: # If explicitly told it's processing (e.g., model load)
             model_is_loaded = False # Assume model not ready during processing start/load


        # --- Calculate Enable States ---
        can_process = image_loaded and scale_is_set and model_is_loaded and not processing
        can_recalculate = annotations_exist and scale_is_set and not processing
        can_save_results = results_exist and not processing
        can_save_view = self.pixmap_item is not None and not processing
        can_export_coco = annotations_exist and not processing
        can_plot = results_exist and not processing
        can_crop = image_loaded and not processing
        can_save_scale = scale_is_set and not processing
        can_load_scale = image_loaded and not processing
        can_reset_scale = scale_is_set and not processing
        can_reset_app = not processing
        can_interact = not processing # General flag for UI controls
        # Add state for patch segmentation
        can_patch_segment = image_loaded and scale_is_set and model_is_loaded and not processing

        # --- Update Actions ---
        if hasattr(self, 'crop_action'): self.crop_action.setEnabled(can_crop)
        if hasattr(self, 'save_results_action'): self.save_results_action.setEnabled(can_save_results)
        if hasattr(self, 'save_view_action'): self.save_view_action.setEnabled(can_save_view)
        if hasattr(self, 'export_coco_action'): self.export_coco_action.setEnabled(can_export_coco)
        if hasattr(self, 'plot_action'): self.plot_action.setEnabled(can_plot)
        if hasattr(self, 'save_scale_action'): self.save_scale_action.setEnabled(can_save_scale)
        if hasattr(self, 'load_scale_action'): self.load_scale_action.setEnabled(can_load_scale)
        if hasattr(self, 'reset_scale_action'): self.reset_scale_action.setEnabled(can_reset_scale)
        if hasattr(self, 'reset_action'): self.reset_action.setEnabled(can_reset_app)
        if hasattr(self, 'segment_action'): self.segment_action.setEnabled(can_patch_segment)
        # Zoom actions depend only on view existing
        can_zoom = hasattr(self, 'view')
        if hasattr(self, 'zoom_in_action'): self.zoom_in_action.setEnabled(can_zoom)
        if hasattr(self, 'zoom_out_action'): self.zoom_out_action.setEnabled(can_zoom)
        if hasattr(self, 'reset_zoom_action'): self.reset_zoom_action.setEnabled(can_zoom)


        # --- Update Buttons & Controls ---
        if hasattr(self, 'process_button'): self.process_button.setEnabled(can_process)
        if hasattr(self, 'recalculate_button'): self.recalculate_button.setEnabled(can_recalculate)
        # Delete button state is handled within ResultsViewWidget based on its selection

        # Disable parameter/mode controls during processing or model loading
        if hasattr(self, 'fastsam_params_widget'): self.fastsam_params_widget.setEnabled(can_interact)
        if hasattr(self, 'mobilesam_params_widget'): self.mobilesam_params_widget.setEnabled(can_interact and MOBILE_SAM_AVAILABLE)
        if hasattr(self, 'fastsam_radio'): self.fastsam_radio.setEnabled(can_interact)
        if hasattr(self, 'mobilesam_radio'): self.mobilesam_radio.setEnabled(can_interact and MOBILE_SAM_AVAILABLE)

        # Scale input fields
        if hasattr(self, 'real_world_length_edit'): self.real_world_length_edit.setEnabled(can_interact)
        if hasattr(self, 'manual_scale_edit'): self.manual_scale_edit.setEnabled(can_interact)
        set_scale_button = self.findChild(QPushButton, "setManualScaleButton")
        if set_scale_button: set_scale_button.setEnabled(can_interact)

        # Toolbar mode buttons
        for btn in self.mode_buttons.values(): btn.setEnabled(can_interact)

        logger.debug(f"Action states updated. Processing: {processing}, Model: {model_is_loaded}, Img: {image_loaded}, Anno: {annotations_exist}, Scale: {scale_is_set}, Results: {results_exist}")


    @Slot(str, str) # Make it a slot
    def _show_error_message(self, title: str, message: str):
        """Helper to show QMessageBox critical error."""
        # Ensure this runs in the main thread if called from worker
        if QThread.currentThread() != self.thread():
             QMetaObject.invokeMethod(self, "_show_error_message", Qt.QueuedConnection,
                                     Q_ARG(str, title), Q_ARG(str, message))
        else:
             QMessageBox.critical(self, title, message)


    # --- Event Handlers ---
    def keyPressEvent(self, event: QtGui.QKeyEvent):
        """Handles global keyboard shortcuts."""
        key = event.key()
        modifiers = event.modifiers()

        # Delete selected items (check button enabled state)
        if (key == Qt.Key_Delete or key == Qt.Key_Backspace) and self.results_widget.delete_button.isEnabled():
             self.results_widget._on_delete_clicked() # Trigger delete via results widget
             event.accept()
             return

        # Escape key handling
        elif key == Qt.Key_Escape:
             # If selection mode and items are selected, deselect all
             if self.view.mode == CustomGraphicsView.MODE_SELECTION and self.results_widget.selected_df_indices:
                  self.results_widget.set_selected_indices(set())
                  event.accept()
                  return
             # If scale mode and drawing, cancel drawing
             elif self.view.mode == CustomGraphicsView.MODE_SCALE and self.view.scale_line_item and self.view.scale_line_item.pen().style() == Qt.DashLine:
                  self.view.clear_scale_line_item(temporary_only=True)
                  self.view.scale_line_start = None # Reset drawing state
                  self.view.setCursor(self.view._cross_cursor) # Reset cursor
                  event.accept()
                  return

        super().keyPressEvent(event) # Pass on to default handler


    def closeEvent(self, event: QtGui.QCloseEvent):
        """Handles the window close event."""
        logger.info("Close event triggered.")
        self.stop_processing_thread() # Attempt to stop worker

        # Check for unsaved data (simplified check)
        if self.df is not None and not self.df.empty:
            reply = QMessageBox.question(
                self, "Exit Confirmation", "Exit GrainSight?\nUnsaved results will be lost.",
                QMessageBox.Yes | QMessageBox.Cancel, QMessageBox.Cancel
            )
            if reply == QMessageBox.Cancel:
                event.ignore(); return

        logger.info("Proceeding with application close.")
        self.cleanup_before_exit()
        event.accept()

    def cleanup_before_exit(self):
        """Performs cleanup operations before the application exits."""
        logger.info("Performing cleanup before exit...")
        save_theme_preference(self.theme_file, self.current_theme)

        # Close plot dialogs
        for dialog in self.plot_dialogs[:]:
             if dialog: dialog.close()
        self.plot_dialogs.clear()
        try:
            import matplotlib.pyplot as plt
            plt.close('all')
        except Exception as e:
            logger.error(f"Error closing matplotlib figures: {e}")

        # Release model resources (optional, GC usually handles it)
        if hasattr(self, 'model'): del self.model; self.model = None
        if self.device.type == 'cuda':
             try: torch.cuda.empty_cache(); logger.debug("Cleared CUDA cache.")
             except: pass # Ignore errors here

        # Close logging handlers? Usually handled by basicConfig on exit
        logger.info("Cleanup complete. Exiting.")
