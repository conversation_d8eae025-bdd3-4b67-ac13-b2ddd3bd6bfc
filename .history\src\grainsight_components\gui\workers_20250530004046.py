# gui/workers.py

import logging
import time # Keep time for potential delays if needed
import numpy as np
import torch
import pandas as pd
from PIL import Image
from typing import List, Tuple, Optional, Union

from PySide6.QtCore import QObject, Signal, Slot, QThread # Keep QThread for type hint if needed

# --- Core Logic Imports ---
# Assumes core modules are in a directory accessible via Python's path
try:
    from src.grainsight_components.core.segmentation import segment_image
    from src.grainsight_components.core.analysis import calculate_parameters
    from src.grainsight_components.core.image_utils import create_segmented_visualization
    from src.grainsight_components.core.patching import generate_patch_coordinates, merge_patch_results # Import patching utils
    from src.grainsight_components.core.patch_refinement import intelligent_patch_merge # Import new intelligent patch merging
except ImportError as e:
     # Handle error - maybe raise a critical error or log heavily
     # This indicates a problem with the project structure or PYTHONPATH
     print(f"FATAL ERROR: Could not import core modules in worker: {e}")
     # In a real app, you might want to prevent the app from starting
     # or show an immediate error message box. For now, define stubs.
     def segment_image(*args, **kwargs): raise NotImplementedError("Core segmentation missing")
     def calculate_parameters(*args, **kwargs): raise NotImplementedError("Core analysis missing")
     def create_segmented_visualization(*args, **kwargs): raise NotImplementedError("Core image utils missing")
     def generate_patch_coordinates(*args, **kwargs): raise NotImplementedError("Core patching missing")
     def merge_patch_results(*args, **kwargs): raise NotImplementedError("Core patching missing")
     def intelligent_patch_merge(*args, **kwargs): raise NotImplementedError("Core patch refinement missing")


logger = logging.getLogger(__name__)

class ProcessingWorker(QObject):
    """
    Worker object to perform segmentation and analysis in a background thread.
    """
    # Signals emitted by the worker
    # finished(df_results, annotations_tensor, visualization_image)
    finished = Signal(object, object, object)
    progress = Signal(int) # Percentage 0-100
    error = Signal(str, str)   # Error message string (title, message)
    status = Signal(str)  # Status update message string

    def __init__(self, pil_image: Image.Image, model: object, device: torch.device,
                 scale_factor: float, params: dict):
        super().__init__()
        self.pil_image = pil_image
        self.model = model # Can be YOLO or base SAM model
        self.device = device
        self.scale_factor = scale_factor
        self.params = params # Contains model_type and specific params
        self._is_running = True # Flag to allow graceful stopping

    def stop(self):
        """Signals the worker to stop processing."""
        logger.info("Processing worker received stop signal.")
        self._is_running = False

    @Slot()
    def run(self):
        """Executes the segmentation and parameter calculation."""
        try:
            start_time = time.time()
            model_type = self.params.get('model_type', 'unknown')
            logger.info(f"Worker started processing with {model_type.upper()} model.")

            if not self._is_running: return
            self.status.emit("Starting segmentation...")
            self.progress.emit(5)

            # --- 1. Segmentation ---
            # Move the model to the target device *within the worker* for this run
            # This assumes the main thread keeps the model on CPU usually
            try:
                logger.debug(f"Moving model ({type(self.model).__name__}) to device: {self.device}")
                model_on_device = self.model.to(self.device)
                # Set model to evaluation mode
                if hasattr(model_on_device, 'eval'):
                    model_on_device.eval()
                logger.debug("Model moved to device and set to eval mode.")
            except Exception as model_move_e:
                 logger.error(f"Failed to move model to device {self.device}: {model_move_e}", exc_info=True)
                 self.error.emit("Model Error", f"Error moving model to {self.device}: {model_move_e}")
                 self.finished.emit(None, None, None)
                 return

            # Call the core segmentation function
            try:
                raw_results, annotations_full_res = segment_image(
                    self.pil_image, model_on_device, self.device, self.params
                )
            finally:
                 # Ensure model is moved back to CPU if it was on GPU, to free VRAM
                 if self.device.type == 'cuda':
                      try:
                           logger.debug("Moving model back to CPU.")
                           self.model.to('cpu') # Move the original self.model back
                           # Optional: Clear cache if memory is tight
                           torch.cuda.empty_cache()
                           logger.debug("CUDA cache cleared after moving model to CPU.")
                      except Exception as cpu_move_e:
                           logger.warning(f"Could not move model back to CPU: {cpu_move_e}")
                 # Clear the reference to the potentially large model on device
                 del model_on_device


            if not self._is_running: return

            # --- Check Segmentation Results ---
            num_annotations = 0
            if annotations_full_res is not None and isinstance(annotations_full_res, torch.Tensor):
                num_annotations = annotations_full_res.shape[0]

            if num_annotations == 0:
                 logger.warning(f"No objects detected during {model_type} segmentation.")
                 self.error.emit(f"No objects detected using {model_type.upper()}.")
                 self.finished.emit(None, None, None)
                 return

            self.status.emit(f"{model_type.upper()} found {num_annotations} objects. Calculating parameters...")
            self.progress.emit(40)


            # --- 2. Parameter Calculation ---
            # The calculate_parameters function expects annotations (list or tensor)
            # and handles moving to CPU internally if needed for OpenCV.
            df, valid_mask = calculate_parameters(annotations_full_res, self.scale_factor, self.progress.emit)

            if not self._is_running: return
            if df is None or df.empty:
                logger.warning("No valid objects found after parameter calculation.")
                self.error.emit("No valid objects found after parameter calculation.")
                self.finished.emit(None, None, None)
                return

            # --- Filter Annotations based on valid_mask ---
            # Ensure the final annotations passed back match the DataFrame
            try:
                 if len(valid_mask) != num_annotations:
                      logger.error(f"CRITICAL: Mismatch between valid_mask ({len(valid_mask)}) and initial annotations ({num_annotations})")
                      raise RuntimeError("Mask count mismatch after parameter calculation.")

                 # Get the actual annotations that correspond to the valid rows in the DataFrame
                 # Since df index maps to original annotation index, we can use it.
                 valid_indices = df.index.to_list() # Get original indices that are valid
                 if isinstance(annotations_full_res, torch.Tensor):
                      final_annotations = annotations_full_res[valid_indices]
                 elif isinstance(annotations_full_res, list): # If segmentation returned a list
                       final_annotations = [annotations_full_res[i] for i in valid_indices]
                 else:
                      raise TypeError("Unsupported annotation type for filtering.")

                 logger.info(f"Filtered annotations to {len(df)} valid objects.")

            except Exception as filter_e:
                 logger.exception("Error filtering annotations based on valid_mask:")
                 self.error.emit(f"Internal error filtering results: {filter_e}")
                 self.finished.emit(None, None, None)
                 return


            self.status.emit(f"Parameters complete ({len(df)} valid objects). Creating visualization...")
            self.progress.emit(90)


            # --- 3. Create Visualization ---
            # Use the filtered annotations and the original base image
            contour_thickness = self.params.get('contour_thickness', 1)
            segmented_image_vis = create_segmented_visualization(
                self.pil_image,
                final_annotations, # Use only the valid annotations
                contour_thickness=contour_thickness
            )

            if not self._is_running: return

            end_time = time.time()
            processing_time = end_time - start_time
            self.progress.emit(100)
            self.status.emit(f"Processing complete ({len(df)} grains) in {processing_time:.2f}s.")
            logger.info(f"Worker finished successfully in {processing_time:.2f} seconds.")
            self.finished.emit(df, final_annotations, segmented_image_vis)

        except ImportError as imp_err:
             logger.critical(f"Import error in worker thread: {imp_err}", exc_info=True)
             self.error.emit(f"Core component missing: {imp_err}. Check installation.")
             self.finished.emit(None, None, None) # Signal failure
        except NotImplementedError as ni_err:
             logger.critical(f"Core function not implemented: {ni_err}", exc_info=True)
             self.error.emit("Implementation Error", f"Core function error: {ni_err}.")
             self.finished.emit(None, None, None) # Signal failure
        except Exception as e:
            logger.exception("Error occurred in processing worker:")
            self.error.emit("Processing Error", f"An unexpected error occurred: {e}")
            self.finished.emit(None, None, None) # Signal failure with None results


# --- New Patch Processing Worker ---

class PatchProcessingWorker(QObject):
    """
    Worker object for patch-based segmentation and analysis.
    """
    # Signals (same as ProcessingWorker)
    finished = Signal(object, object, object) # df, annotations_tensor, vis_image
    progress = Signal(int)
    error = Signal(str, str) # title, message
    status = Signal(str)

    def __init__(self, pil_image: Image.Image, model: object, device: torch.device,
                 scale_factor: float, seg_params: dict, patch_config: dict):
        super().__init__()
        self.pil_image = pil_image
        self.model = model
        self.device = device # Target device (CPU for FastSAM, CUDA/CPU for MobileSAM)
        self.scale_factor = scale_factor
        self.seg_params = seg_params # Segmentation parameters (iou, conf, etc.)
        self.patch_config = patch_config # {'rows': R, 'cols': C, 'overlap': O}
        self._is_running = True

    def stop(self):
        logger.info("Patch processing worker received stop signal.")
        self._is_running = False

    @Slot()
    def run(self):
        """Executes patch-based segmentation, merging, and analysis."""
        try:
            start_time = time.time()
            model_type = self.seg_params.get('model_type', 'unknown')
            logger.info(f"Patch worker started processing with {model_type.upper()} model.")
            logger.info(f"Patch Config: {self.patch_config}")
            logger.info(f"Using device: {self.device.type}")

            full_width, full_height = self.pil_image.size
            patch_results = [] # Store results: List[Tuple[Tensor, Offset]]

            # --- Move model to target device ---
            try:
                model_on_device = self.model.to(self.device)
                if hasattr(model_on_device, 'eval'): model_on_device.eval()
                logger.debug("Model moved to device for patch processing.")
            except Exception as model_move_e:
                 self.error.emit("Model Error", f"Error moving model to {self.device}: {model_move_e}")
                 self.finished.emit(None, None, None); return

            # --- Iterate through Patches ---
            try:
                # Generate patch coordinates
                patch_coords = list(generate_patch_coordinates(
                    full_width, full_height,
                    self.patch_config['rows'],
                    self.patch_config['cols'],
                    self.patch_config['overlap']
                ))

                total_patches = len(patch_coords)
                logger.info(f"Processing {total_patches} patches...")
                self.status.emit(f"Processing {total_patches} patches...")

                # Process each patch
                for i, (x_start, y_start, patch_w, patch_h) in enumerate(patch_coords):
                    if not self._is_running: break

                    # Update progress
                    progress_pct = int((i / total_patches) * 70) # Use 0-70% for patch processing
                    self.progress.emit(progress_pct)
                    self.status.emit(f"Processing patch {i+1}/{total_patches}...")

                    # Extract patch from image
                    patch_box = (x_start, y_start, x_start + patch_w, y_start + patch_h)
                    patch_img = self.pil_image.crop(patch_box)

                    # Process patch with segmentation model
                    logger.debug(f"Segmenting patch {i+1}/{total_patches} at ({x_start},{y_start})")
                    try:
                        _, patch_annotations = segment_image(
                            patch_img, model_on_device, self.device, self.seg_params
                        )

                        # Store results with offset information
                        if patch_annotations is not None and (
                            (isinstance(patch_annotations, torch.Tensor) and patch_annotations.shape[0] > 0) or
                            (isinstance(patch_annotations, list) and len(patch_annotations) > 0)
                        ):
                            patch_results.append((patch_annotations, (x_start, y_start)))
                            logger.debug(f"Patch {i+1} yielded {patch_annotations.shape[0] if isinstance(patch_annotations, torch.Tensor) else len(patch_annotations)} annotations")
                        else:
                            logger.debug(f"No objects detected in patch {i+1}")

                    except Exception as patch_e:
                        logger.error(f"Error processing patch {i+1}: {patch_e}", exc_info=True)
                        # Continue with next patch instead of failing completely
                        continue

            finally:
                # Clean up model resources
                if self.device.type == 'cuda':
                    try:
                        self.model.to('cpu')
                        torch.cuda.empty_cache()
                    except Exception as e:
                        logger.warning(f"Error moving model back to CPU: {e}")
                del model_on_device

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            # --- Merge Patch Results ---
            self.status.emit("Merging patch results...")
            self.progress.emit(75)

            if not patch_results:
                logger.warning("No valid annotations found in any patch.")
                self.error.emit("Patch Processing", "No objects detected in any patch.")
                self.finished.emit(None, None, None)
                return

            try:
                # Merge annotations from all patches
                # Log model type for debugging
                model_type = self.seg_params.get('model_type', 'unknown')
                logger.info(f"Merging patches with model type: {model_type}")

                # Check if we're using a single patch (no patching mode)
                is_single_patch = len(patch_results) == 1

                # Check if we should use intelligent patch merging
                use_intelligent_merge = self.seg_params.get('use_intelligent_patch_merge', False)

                if is_single_patch:
                    # Skip intelligent artifact handling for single patch mode
                    logger.info("Single patch mode detected - skipping intelligent artifact handling")

                    # For single patch, just use the annotations directly
                    # This avoids unnecessary processing and improves performance
                    if patch_results[0] is not None:
                        annotations, _ = patch_results[0]
                        # Ensure annotations are on the correct device
                        merged_annotations = annotations.to(self.device)
                        logger.info(f"Using direct annotations from single patch: {merged_annotations.shape}")
                    else:
                        # Handle case where the single patch failed
                        logger.error("Single patch processing failed")
                        merged_annotations = torch.zeros((0, full_height, full_width), dtype=torch.bool, device=self.device)
                elif use_intelligent_merge:
                    # Use our new intelligent patch merging approach for multi-patch mode
                    logger.info("Using intelligent patch merging algorithm")
                    artifact_sensitivity = self.seg_params.get('artifact_sensitivity', 0.5)
                    duplicate_sensitivity = self.seg_params.get('duplicate_sensitivity', 0.7)

                    # Get performance optimization parameters
                    duplicate_detection_timeout = self.seg_params.get('duplicate_detection_timeout', 60)
                    batch_size = self.seg_params.get('batch_size', 100)
                    max_pairs_to_check = self.seg_params.get('max_pairs_to_check', 10000)

                    # Adjust parameters based on number of patches
                    if len(patch_results) > 9:  # For large patch counts (e.g., 3x3 or larger)
                        # Use more aggressive settings to improve performance
                        batch_size = max(batch_size, 150)
                        max_pairs_to_check = min(max_pairs_to_check, 5000)

                    logger.info(f"Artifact sensitivity: {artifact_sensitivity:.2f}, Duplicate sensitivity: {duplicate_sensitivity:.2f}")
                    logger.info(f"Performance settings: timeout={duplicate_detection_timeout}s, batch_size={batch_size}, max_pairs={max_pairs_to_check}")

                    merged_annotations = intelligent_patch_merge(
                        patch_annotations=patch_results,
                        full_image_shape=(full_height, full_width),
                        device=self.device,
                        seg_params=self.seg_params, # Pass the whole seg_params dict
                        patch_config=self.patch_config # Pass the whole patch_config dict
                        # iou_threshold_refinement and min_area_refinement will use defaults
                        # or can be added to seg_params if specific control is needed from GUI
                    )
                else:
                    # Use the legacy NMS-based approach
                    logger.info("Using legacy NMS-based patch merging")

                    # Adjust parameters based on model type
                    filter_subgrains = self.seg_params.get('filter_subgrains', True)

                    # For MobileSAM, we might need to be more conservative with subgrain filtering
                    if model_type == 'mobilesam':
                        logger.info("Using MobileSAM-specific parameters for subgrain filtering")
                        min_straight_edge_ratio = self.seg_params.get('min_straight_edge_ratio', 0.25) * 0.8  # 20% more lenient
                        subgrain_parallel_edges = max(1, self.seg_params.get('subgrain_parallel_edges', 2) - 1)  # Require one less edge
                    else:  # FastSAM or other models
                        min_straight_edge_ratio = self.seg_params.get('min_straight_edge_ratio', 0.25)
                        subgrain_parallel_edges = self.seg_params.get('subgrain_parallel_edges', 2)

                    # Use default values for obsolete NMS parameters
                    merged_annotations = merge_patch_results(
                        patch_results,
                        (full_height, full_width),
                        self.device,
                        nms_iou_threshold=self.seg_params.get('iou_threshold', 0.5),
                        nms_containment_threshold=0.85,  # Default value for obsolete parameter
                        border_penalty_factor=0.5,       # Default value for obsolete parameter
                        size_ratio_threshold=5.0,        # Default value for obsolete parameter
                        filter_subgrains=filter_subgrains,
                        subgrain_angle_threshold=self.seg_params.get('subgrain_angle_threshold', 5.0),
                        subgrain_edge_straightness=self.seg_params.get('subgrain_edge_straightness', 0.95),
                        subgrain_parallel_edges=subgrain_parallel_edges,
                        min_straight_edge_ratio=min_straight_edge_ratio
                    )

                if merged_annotations.shape[0] == 0:
                    logger.warning("No annotations remained after merging patches.")
                    self.error.emit("No valid objects found after merging patches.")
                    self.finished.emit(None, None, None)
                    return

                logger.info(f"Successfully merged patches: {merged_annotations.shape[0]} objects found.")
                self.status.emit(f"Found {merged_annotations.shape[0]} objects. Calculating parameters...")

            except Exception as merge_e:
                logger.exception(f"Error merging patch results: {merge_e}")
                self.error.emit(f"Error merging patch results: {merge_e}")
                self.finished.emit(None, None, None)
                return

            # --- Calculate Parameters ---
            self.progress.emit(80)
            df, valid_mask = calculate_parameters(merged_annotations, self.scale_factor, self.progress.emit)

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            if df is None or df.empty:
                logger.warning("No valid objects found after parameter calculation.")
                self.error.emit("No valid objects found after parameter calculation.")
                self.finished.emit(None, None, None)
                return

            # Filter annotations to match DataFrame
            try:
                valid_indices = df.index.to_list()
                final_annotations = merged_annotations[valid_indices]
                logger.info(f"Filtered to {len(df)} valid objects after parameter calculation.")
            except Exception as filter_e:
                logger.exception(f"Error filtering merged annotations: {filter_e}")
                self.error.emit(f"Error filtering results: {filter_e}")
                self.finished.emit(None, None, None)
                return

            # --- Create Visualization ---
            self.status.emit(f"Creating visualization for {len(df)} objects...")
            self.progress.emit(95)

            contour_thickness = self.seg_params.get('contour_thickness', 1)
            segmented_image_vis = create_segmented_visualization(
                self.pil_image,
                final_annotations,
                contour_thickness=contour_thickness
            )

            # --- Finish ---
            end_time = time.time()
            processing_time = end_time - start_time
            self.progress.emit(100)
            self.status.emit(f"Patch processing complete: {len(df)} objects in {processing_time:.2f}s")
            logger.info(f"Patch worker finished in {processing_time:.2f} seconds with {len(df)} objects.")
            self.finished.emit(df, final_annotations, segmented_image_vis)

        except ImportError as imp_err:
            logger.critical(f"Import error in patch worker: {imp_err}", exc_info=True)
            self.error.emit(f"Core component missing: {imp_err}. Check installation.")
            self.finished.emit(None, None, None)
        except NotImplementedError as ni_err:
            logger.critical(f"Core function not implemented: {ni_err}", exc_info=True)
            self.error.emit(f"Core function error: {ni_err}.")
            self.finished.emit(None, None, None)
        except Exception as e:
            logger.exception("Error occurred in patch processing worker:")
            self.error.emit(f"Patch processing error: {e}")
            self.finished.emit(None, None, None)