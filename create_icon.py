#!/usr/bin/env python3
"""
Create a simple icon for GrainSight application
"""

import os
from PIL import Image, ImageDraw

def create_grain_icon():
    """Create a simple grain-like icon."""
    
    # Create directory if it doesn't exist
    icon_dir = "src/grainsight_components/gui/assets/icons"
    os.makedirs(icon_dir, exist_ok=True)
    
    # Create a 256x256 icon for better quality
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors for grain-like appearance
    bg_color = (245, 245, 220, 255)  # Beige background
    grain_color = (139, 69, 19, 255)  # Brown grain
    highlight_color = (160, 82, 45, 255)  # Lighter brown
    outline_color = (101, 67, 33, 255)  # Dark brown outline
    
    # Draw background circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color, outline=outline_color, width=4)
    
    # Draw multiple grain-like shapes
    grains = [
        # (x, y, width, height)
        (60, 80, 50, 40),
        (120, 60, 45, 35),
        (90, 130, 55, 45),
        (150, 120, 40, 50),
        (70, 170, 48, 38),
        (140, 170, 42, 35),
        (110, 200, 35, 30),
    ]
    
    for x, y, w, h in grains:
        # Draw grain shadow
        draw.ellipse([x+2, y+2, x+w+2, y+h+2], fill=(0, 0, 0, 50))
        # Draw grain
        draw.ellipse([x, y, x+w, y+h], fill=grain_color, outline=outline_color, width=2)
        # Draw highlight
        draw.ellipse([x+5, y+5, x+w-10, y+h-10], fill=highlight_color)
    
    # Add title text
    try:
        from PIL import ImageFont
        # Try to use a nice font
        try:
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        text = "GrainSight"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        text_x = (size - text_width) // 2
        text_y = size - 40
        
        # Draw text shadow
        draw.text((text_x+1, text_y+1), text, fill=(0, 0, 0, 128), font=font)
        # Draw text
        draw.text((text_x, text_y), text, fill=outline_color, font=font)
    except:
        pass  # Skip text if font loading fails
    
    # Save as PNG first
    png_path = os.path.join(icon_dir, "grain_icon.png")
    img.save(png_path, "PNG")
    print(f"✅ Created PNG icon: {png_path}")
    
    # Save as ICO with multiple sizes
    ico_path = os.path.join(icon_dir, "grain_icon.ico")
    # Create multiple sizes for ICO
    sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    images = []
    
    for ico_size in sizes:
        resized = img.resize(ico_size, Image.Resampling.LANCZOS)
        images.append(resized)
    
    # Save as ICO
    img.save(ico_path, format='ICO', sizes=[(s[0], s[1]) for s in sizes])
    print(f"✅ Created ICO icon: {ico_path}")
    
    return png_path, ico_path

if __name__ == "__main__":
    try:
        png_path, ico_path = create_grain_icon()
        print("🎨 Icon creation completed successfully!")
    except Exception as e:
        print(f"❌ Error creating icon: {e}")
        print("Make sure PIL (Pillow) is installed: pip install Pillow")
