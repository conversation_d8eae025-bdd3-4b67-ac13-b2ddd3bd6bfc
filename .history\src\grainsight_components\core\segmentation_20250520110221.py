# core/segmentation.py

import logging
import numpy as np
import torch
from PIL import Image

# Import specific model types or necessary components
from ultralytics import YOL<PERSON>
# Use our wrapper module for MobileSAM
from src.utils.mobilesam_wrapper import is_available as is_mobilesam_available
from src.utils.mobilesam_wrapper import get_sam_automatic_mask_generator

# Check if MobileSAM is available without importing it
MOBILE_SAM_AVAILABLE = is_mobilesam_available()
# Make pycocotools import conditional within the function where it's used
# try:
#     from pycocotools import mask as mask_utils
#     PYCOCOTOOLS_AVAILABLE = True
# except ImportError:
#     PYCOCOTOOLS_AVAILABLE = False
#     mask_utils = None

logger = logging.getLogger(__name__)

# Default parameters (can be overridden by caller)
DEFAULT_IOU_THRESHOLD = 0.7
DEFAULT_CONF_THRESHOLD = 0.5
DEFAULT_MAX_DET = 500
DEFAULT_MOBILE_SAM_POINTS_PER_SIDE = 32
DEFAULT_MOBILE_SAM_PRED_IOU_THRESH = 0.88
DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH = 0.95
DEFAULT_MOBILE_SAM_BOX_NMS_THRESH = 0.3
DEFAULT_MOBILE_SAM_MIN_MASK_AREA = 0

def segment_image(pil_image: Image.Image, model: object, device: torch.device, params: dict) -> tuple:
    """
    Performs segmentation on the input PIL image using either FastSAM (YOLO) or MobileSAM.

    Args:
        pil_image (PIL.Image.Image): The input image.
        model (object): The loaded model instance (YOLO for FastSAM, base SAM for MobileSAM).
        device (torch.device): The device to run inference on (e.g., 'cuda' or 'cpu').
        params (dict): Dictionary containing model-specific parameters.
            Required keys depend on 'model_type'.
            'model_type': 'fastsam' or 'mobilesam'.
            For 'fastsam': 'input_size', 'iou', 'conf', 'max_det'.
            For 'mobilesam': 'points_per_side', 'pred_iou_thresh',
                             'stability_score_thresh', 'box_nms_thresh',
                             'crop_n_layers', 'crop_n_points_downscale_factor',
                             'min_mask_region_area' (optional).

    Returns:
        tuple: (raw_results, annotations_tensor)
            - raw_results (object): Model-specific raw output (e.g., YOLO results list or SAM mask data list).
            - annotations_tensor (torch.Tensor): Tensor of binary masks (N, H, W) on the specified device,
                                                 resized to the original image dimensions. Returns empty tensor on failure.

    Raises:
        ValueError: If required parameters are missing or invalid.
        TypeError: If the provided model object doesn't match the expected type for the 'model_type'.
        ImportError: If required libraries (mobile_sam, pycocotools) are not installed for the chosen model type.
    """
    original_w, original_h = pil_image.size
    model_type = params.get('model_type')

    if not model_type:
        raise ValueError("Missing 'model_type' in parameters ('fastsam' or 'mobilesam').")

    logger.info(f"Starting segmentation with {model_type.upper()} model.")

    # --- Ensure image is in a compatible format (RGB) ---
    try:
        if pil_image.mode != 'RGB':
            image_rgb = pil_image.convert('RGB')
            logger.debug(f"Converted input image from {pil_image.mode} to RGB for processing.")
        else:
            image_rgb = pil_image
        np_image_rgb = np.array(image_rgb) # HWC format
    except Exception as img_conv_e:
        logger.error(f"Failed to convert input image to NumPy array: {img_conv_e}", exc_info=True)
        raise ValueError(f"Failed to convert input image to NumPy array: {img_conv_e}")

    # --- Model Specific Logic ---
    raw_results = None
    annotations_tensor = torch.empty(0, device=device) # Default empty tensor

    if model_type == "fastsam":
        # --- FastSAM (YOLO) ---
        if not isinstance(model, YOLO):
            raise TypeError(f"Model type mismatch: Expected YOLO model for FastSAM, got {type(model).__name__}.")

        required = ['input_size', 'iou', 'conf', 'max_det']
        if not all(p in params for p in required):
            missing = [p for p in required if p not in params]
            raise ValueError(f"Missing required FastSAM parameters: {missing}")

        input_size = params['input_size']
        iou_thresh = params.get('iou', DEFAULT_IOU_THRESHOLD)
        conf_thresh = params.get('conf', DEFAULT_CONF_THRESHOLD)
        max_det = params.get('max_det', DEFAULT_MAX_DET)

        logger.info(f"FastSAM Params: input_size={input_size}, iou={iou_thresh:.2f}, conf={conf_thresh:.2f}, max_det={max_det}")

        # The GUI worker should handle moving the model to the device before calling this.
        # Assume model is already on the correct device or CPU.
        # Perform inference
        try:
            results = model(image_rgb, # Pass PIL RGB image
                           retina_masks=True,
                           iou=iou_thresh,
                           conf=conf_thresh,
                           imgsz=input_size,
                           max_det=max_det,
                           verbose=False,
                           device=device) # Explicitly pass device
            raw_results = results

            if not results or results[0].masks is None:
                logger.warning("FastSAM segmentation returned no masks.")
                return raw_results, annotations_tensor # Return empty tensor

            # Get masks (potentially on GPU)
            masks_tensor_resized = results[0].masks.data # Shape: (N, H_resized, W_resized)

            # Resize masks back to original image dimensions using torch
            masks_full_res = torch.nn.functional.interpolate(
                masks_tensor_resized.unsqueeze(1), # Add channel dim: (N, 1, H_r, W_r)
                size=(original_h, original_w),
                mode='nearest' # Use nearest for masks
            ).squeeze(1) # Remove channel dim: (N, H_orig, W_orig)

            # Binarize masks (model might output probabilities)
            annotations_tensor = (masks_full_res > 0.5).byte() # Threshold at 0.5, convert to byte tensor
            logger.info(f"FastSAM segmentation finished. Found {len(annotations_tensor)} initial masks.")

        except Exception as e:
            logger.error(f"Error during FastSAM inference or mask processing: {e}", exc_info=True)
            # Return raw results (if any) and empty tensor
            return raw_results, torch.empty(0, device=device)

    elif model_type == "mobilesam":
        # --- MobileSAM (SamAutomaticMaskGenerator) ---
        if not MOBILE_SAM_AVAILABLE:
             raise ImportError("MobileSAM components not found. Please install 'mobile_sam'.")

        # Check if model is the base SAM model (this check might need refinement)
        if isinstance(model, YOLO):
             raise TypeError(f"Model type mismatch: Expected base SAM model for MobileSAM, got {type(model).__name__}.")

        required = ['points_per_side', 'pred_iou_thresh', 'stability_score_thresh',
                    'box_nms_thresh', 'crop_n_layers', 'crop_n_points_downscale_factor']
        if not all(p in params for p in required):
            missing = [p for p in required if p not in params]
            raise ValueError(f"Missing required MobileSAM parameters: {missing}")

        generator_params = {
            'points_per_side': params.get('points_per_side', DEFAULT_MOBILE_SAM_POINTS_PER_SIDE),
            'pred_iou_thresh': params.get('pred_iou_thresh', DEFAULT_MOBILE_SAM_PRED_IOU_THRESH),
            'stability_score_thresh': params.get('stability_score_thresh', DEFAULT_MOBILE_SAM_STABILITY_SCORE_THRESH),
            'box_nms_thresh': params.get('box_nms_thresh', DEFAULT_MOBILE_SAM_BOX_NMS_THRESH),
            'crop_n_layers': params.get('crop_n_layers', 0),
            'crop_n_points_downscale_factor': params.get('crop_n_points_downscale_factor', 1),
            'min_mask_region_area': params.get('min_mask_region_area', DEFAULT_MOBILE_SAM_MIN_MASK_AREA)
        }
        logger.info(f"MobileSAM Params: {generator_params}")

        # The GUI worker should move the base SAM model to the device. Assume it's done.
        # Get the SamAutomaticMaskGenerator class from our wrapper
        SamAutomaticMaskGenerator = get_sam_automatic_mask_generator()
        if SamAutomaticMaskGenerator is None:
            raise ImportError("Failed to get SamAutomaticMaskGenerator from wrapper")

        # Instantiate the generator HERE, using the potentially device-moved model.
        try:
             mask_generator = SamAutomaticMaskGenerator(model=model, **generator_params)

             # Run MobileSAM Segmentation
             logger.info("Starting MobileSAM segmentation generate...")
             # Ensure input is uint8 numpy array (HWC RGB)
             if np_image_rgb.dtype != np.uint8:
                 logger.warning(f"NumPy image dtype is {np_image_rgb.dtype}, converting to uint8 for SAM.")
                 np_image_rgb = np_image_rgb.astype(np.uint8)

             mask_data = mask_generator.generate(np_image_rgb)
             raw_results = mask_data # Store the list of dicts

             logger.info(f"MobileSAM segmentation generate finished. Found {len(mask_data)} potential masks.")

             # Process mask_data to create annotations_tensor
             if not mask_data:
                 logger.warning("MobileSAM segmentation returned no masks (empty list).")
                 return raw_results, annotations_tensor

             masks_list = []
             # Import pycocotools here, only if needed
             mask_utils = None
             if any(isinstance(m.get('segmentation'), dict) for m in mask_data):
                 try:
                     from pycocotools import mask as pycoco_mask_utils
                     mask_utils = pycoco_mask_utils
                     logger.debug("Pycocotools imported successfully for RLE decoding.")
                 except ImportError:
                     logger.error("pycocotools not installed, cannot decode RLE masks from MobileSAM.")
                     # Decide behavior: raise error or skip RLE masks
                     # For now, let it potentially fail later if mask_utils is None when needed


             for i, mask_dict in enumerate(mask_data):
                 if 'segmentation' not in mask_dict:
                     logger.warning(f"Mask dictionary {i} missing 'segmentation' key. Skipping.")
                     continue

                 seg_data = mask_dict['segmentation']
                 binary_mask_np = None

                 if isinstance(seg_data, dict):  # RLE format
                     if mask_utils:
                         try:
                             binary_mask_np = mask_utils.decode(seg_data) # Returns HxW NumPy array (uint8)
                         except Exception as rle_e:
                             logger.error(f"Error decoding RLE mask {i}: {rle_e}")
                             continue
                     else:
                         logger.warning(f"Skipping RLE mask {i} because pycocotools is not available.")
                         continue
                 elif isinstance(seg_data, np.ndarray):
                     if seg_data.dtype == bool:
                         binary_mask_np = seg_data.astype(np.uint8) # Convert boolean to uint8
                     elif seg_data.dtype == np.uint8:
                         binary_mask_np = seg_data # Already uint8
                     else:
                         logger.warning(f"Unexpected NumPy mask dtype in mask {i}: {seg_data.dtype}. Skipping.")
                         continue
                 else:
                     logger.warning(f"Unexpected segmentation format in mask {i}: {type(seg_data)}. Skipping.")
                     continue

                 # Validate shape
                 if binary_mask_np is None or binary_mask_np.ndim != 2 or \
                    binary_mask_np.shape[0] != original_h or binary_mask_np.shape[1] != original_w:
                     logger.warning(f"MobileSAM mask {i} shape mismatch or invalid. Got: {binary_mask_np.shape if binary_mask_np is not None else 'None'}, Expected: {(original_h, original_w)}. Skipping.")
                     continue

                 # Convert valid NumPy mask to tensor on the correct device
                 mask_tensor = torch.from_numpy(binary_mask_np).to(device)
                 masks_list.append(mask_tensor)

             if masks_list:
                 annotations_tensor = torch.stack(masks_list)
                 logger.info(f"Successfully converted {len(masks_list)} MobileSAM masks to tensor.")
             else:
                 logger.warning("No valid MobileSAM masks were converted to tensor.")
                 # annotations_tensor remains empty

        except Exception as e:
             logger.error(f"Error during MobileSAM processing: {e}", exc_info=True)
             # Return raw results (if any) and empty tensor
             return raw_results, torch.empty(0, device=device)

    else:
        raise ValueError(f"Unknown model_type specified: {model_type}")

    logger.info(f"Segmentation process completed for {model_type.upper()}. Returning {len(annotations_tensor)} annotations.")
    return raw_results, annotations_tensor