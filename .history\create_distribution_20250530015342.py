#!/usr/bin/env python3
"""
Create a complete distribution package for GrainSight
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_installer():
    """Create a comprehensive installer script."""
    print("📦 Creating installer script...")
    
    installer_content = '''@echo off
title GrainSight Installation
color 0A
echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    GrainSight Installer                     ║
echo  ║                  Grain Analysis Software                    ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

set "INSTALL_DIR=%PROGRAMFILES%\\GrainSight"
set "DESKTOP_LINK=%USERPROFILE%\\Desktop\\GrainSight.lnk"
set "START_MENU_DIR=%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"

echo Choose installation option:
echo.
echo [1] Install to Program Files (Recommended)
echo [2] Portable installation (current folder)
echo [3] Exit
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" goto install_system
if "%choice%"=="2" goto install_portable
if "%choice%"=="3" goto exit
echo Invalid choice. Please try again.
pause
goto start

:install_system
echo.
echo Installing GrainSight to: %INSTALL_DIR%
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo This installation requires administrator privileges.
    echo Please run this installer as Administrator.
    echo.
    echo Right-click on this file and select "Run as administrator"
    pause
    exit /b 1
)

REM Create installation directory
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy all files
echo Copying application files...
xcopy "GrainSight" "%INSTALL_DIR%" /E /I /Y >nul
copy "GrainSight.exe" "%INSTALL_DIR%\\GrainSight.exe" >nul
copy "Launch_GrainSight.bat" "%INSTALL_DIR%\\Launch_GrainSight.bat" >nul
copy "README.txt" "%INSTALL_DIR%\\README.txt" >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP_LINK%'); $Shortcut.TargetPath = '%INSTALL_DIR%\\GrainSight.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'GrainSight - Grain Analysis Tool'; $Shortcut.Save()"

REM Create start menu shortcut
echo Creating start menu shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU_DIR%\\GrainSight.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\GrainSight.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'GrainSight - Grain Analysis Tool'; $Shortcut.Save()"

REM Add to Windows Programs list
echo Creating uninstaller...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GrainSight" /v "DisplayName" /t REG_SZ /d "GrainSight" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GrainSight" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\\uninstall.bat" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GrainSight" /v "DisplayVersion" /t REG_SZ /d "5.0" /f >nul
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GrainSight" /v "Publisher" /t REG_SZ /d "GrainSight Team" /f >nul

REM Create uninstaller
echo @echo off > "%INSTALL_DIR%\\uninstall.bat"
echo title GrainSight Uninstaller >> "%INSTALL_DIR%\\uninstall.bat"
echo echo Removing GrainSight... >> "%INSTALL_DIR%\\uninstall.bat"
echo del "%DESKTOP_LINK%" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo del "%START_MENU_DIR%\\GrainSight.lnk" 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GrainSight" /f 2^>nul >> "%INSTALL_DIR%\\uninstall.bat"
echo rd /s /q "%INSTALL_DIR%" >> "%INSTALL_DIR%\\uninstall.bat"
echo echo GrainSight has been uninstalled. >> "%INSTALL_DIR%\\uninstall.bat"
echo pause >> "%INSTALL_DIR%\\uninstall.bat"

echo.
echo ✅ Installation completed successfully!
echo.
echo GrainSight has been installed to: %INSTALL_DIR%
echo Desktop shortcut created: %DESKTOP_LINK%
echo Start menu shortcut created
echo.
echo You can now run GrainSight from:
echo - Desktop shortcut
echo - Start menu
echo - Programs and Features (for uninstall)
echo.
goto end

:install_portable
echo.
echo ✅ Portable installation selected!
echo.
echo GrainSight is ready to use from this folder.
echo Simply run "Launch_GrainSight.bat" to start the application.
echo.
echo Note: Keep all files in this folder together for the application to work.
echo.
goto end

:exit
echo Installation cancelled.
goto end

:end
echo.
echo Thank you for using GrainSight!
echo.
pause
'''
    
    with open("dist/Install_GrainSight.bat", "w") as f:
        f.write(installer_content)
    
    print("✅ Installer created: dist/Install_GrainSight.bat")

def create_user_guide():
    """Create a comprehensive user guide."""
    print("📄 Creating user guide...")
    
    guide_content = '''# GrainSight v5.0 - User Guide

## What is GrainSight?

GrainSight is a powerful grain analysis software that uses advanced AI models (FastSAM and MobileSAM) to automatically segment and analyze grain structures in microscopic images. It provides detailed measurements and statistics for grain size analysis.

## Installation

### Option 1: System Installation (Recommended)
1. Run "Install_GrainSight.bat" as Administrator
2. Choose option [1] for system installation
3. Follow the prompts
4. Use desktop or start menu shortcuts to launch

### Option 2: Portable Usage
1. Run "Install_GrainSight.bat"
2. Choose option [2] for portable installation
3. Run "Launch_GrainSight.bat" to start the application

## First Time Setup

When you first run GrainSight, you may need to download the AI model:

1. If prompted, download FastSAM-x.pt from: https://github.com/CASIA-IVA-Lab/FastSAM/releases
2. The application will guide you to place it in the correct location
3. Restart the application after placing the model file

## How to Use GrainSight

### Step 1: Load Your Image
- Click "Load Image" button
- Select your grain microscopy image (supports JPG, PNG, TIFF, BMP)
- The image will be displayed in the main view

### Step 2: Set the Scale
- Click the "Scale" tool in the toolbar
- Draw a line on a known measurement reference in your image
- Enter the real-world length of that line
- This calibrates the pixel-to-micrometer conversion

### Step 3: Choose Analysis Model
- **FastSAM**: Faster processing, good for most grain types
- **MobileSAM**: More accurate but slower, better for complex grain boundaries

### Step 4: Configure Parameters
- **Input Size**: Larger values = more detail but slower processing
- **Confidence**: Higher values = fewer but more confident detections
- **IoU Threshold**: Controls overlap detection sensitivity

### Step 5: Run Analysis
- Click "Run Segmentation" to start the analysis
- For large images, enable "Patch Processing" for better performance
- Wait for processing to complete

### Step 6: Review Results
- Segmented grains will be highlighted with colored overlays
- Click on individual grains to see detailed measurements
- Use the results panel to view statistics

### Step 7: Export Data
- **Save Results**: Export measurements as CSV file
- **Save Image**: Export the segmented image
- **Generate Report**: Create a comprehensive analysis report

## Advanced Features

### Patch Processing
For very large images (>2000x2000 pixels):
- Enable "Patch Processing" in the segmentation options
- Adjust patch grid size (3x3 recommended)
- Set overlap percentage (15% recommended)

### Grain Selection and Filtering
- Click individual grains to select/deselect them
- Use "Select All" or "Clear Selection" buttons
- Filter grains by size, shape, or other parameters

### Measurement Parameters
GrainSight calculates:
- **Area**: Grain area in square micrometers
- **Perimeter**: Grain boundary length in micrometers
- **Equivalent Diameter**: Diameter of circle with same area
- **Aspect Ratio**: Length-to-width ratio
- **Circularity**: How close to a perfect circle (0-1)
- **Solidity**: Ratio of grain area to convex hull area

## Troubleshooting

### Application Won't Start
- Make sure you have Windows 10 or later (64-bit)
- Try running as Administrator
- Check that antivirus isn't blocking the executable
- Ensure all files stay in the same folder

### Model Loading Errors
- Download the correct FastSAM-x.pt model file
- Place it in the models folder as instructed
- Restart the application
- Check available disk space (need ~500MB free)

### Out of Memory Errors
- Use smaller images or enable patch processing
- Close other applications to free RAM
- Reduce the input size parameter
- Consider using a computer with more RAM

### Poor Segmentation Results
- Try adjusting the confidence threshold
- Switch between FastSAM and MobileSAM models
- Ensure your image has good contrast
- Check that the scale is set correctly

### Performance Issues
- Enable patch processing for large images
- Reduce input size parameter
- Close unnecessary applications
- Use an SSD for better file access speed

## Tips for Best Results

### Image Quality
- Use high-contrast images with clear grain boundaries
- Ensure good lighting and focus
- Avoid shadows or uneven illumination
- Recommended resolution: 1000-4000 pixels per side

### Scale Setting
- Always set the scale before analysis
- Use a clear, straight reference line
- Double-check the measurement units
- Re-set scale if changing magnification

### Model Selection
- Start with FastSAM for quick results
- Use MobileSAM for higher accuracy needs
- Experiment with different parameters
- Save successful parameter sets for future use

## System Requirements

### Minimum Requirements
- Windows 10 (64-bit)
- 4GB RAM
- 2GB free disk space
- Graphics card with OpenGL support

### Recommended Requirements
- Windows 11 (64-bit)
- 8GB+ RAM
- 4GB+ free disk space
- Dedicated graphics card
- SSD storage

## Support and Updates

For technical support, bug reports, or feature requests:
- Check the application logs for error details
- Include your system specifications
- Provide sample images if possible

## Version Information

GrainSight v5.0
- Built with PyInstaller for Windows
- Includes FastSAM and MobileSAM AI models
- Supports batch processing and advanced analysis
- Compatible with Windows 10/11 (64-bit)

---

Thank you for using GrainSight!
'''
    
    with open("dist/User_Guide.txt", "w") as f:
        f.write(guide_content)
    
    print("✅ User guide created: dist/User_Guide.txt")

def create_distribution_zip():
    """Create a ZIP file for distribution."""
    print("📦 Creating distribution ZIP file...")
    
    zip_filename = "GrainSight_v5.0_Windows.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all files from dist folder
        for root, dirs, files in os.walk("dist"):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, "dist")
                zipf.write(file_path, arc_name)
                print(f"  Added: {arc_name}")
    
    size_mb = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"✅ Distribution ZIP created: {zip_filename} ({size_mb:.1f} MB)")
    
    return zip_filename

def main():
    """Create complete distribution package."""
    print("📦 Creating GrainSight Distribution Package")
    print("=" * 50)
    
    # Check if dist folder exists
    if not os.path.exists("dist"):
        print("❌ dist folder not found. Please run build_simple.py first.")
        return False
    
    # Create installer
    create_installer()
    
    # Create user guide
    create_user_guide()
    
    # Create distribution ZIP
    zip_file = create_distribution_zip()
    
    print("\n🎉 Distribution package created successfully!")
    print("\nFiles created:")
    print("- dist/Install_GrainSight.bat (Comprehensive installer)")
    print("- dist/User_Guide.txt (Complete user documentation)")
    print(f"- {zip_file} (Ready-to-distribute ZIP file)")
    
    print("\n📋 Distribution Instructions:")
    print("1. Share the ZIP file with users")
    print("2. Users should extract the ZIP file")
    print("3. Users run Install_GrainSight.bat for installation")
    print("4. Or users can run Launch_GrainSight.bat for portable usage")
    
    print("\n📊 Package Contents:")
    print("- GrainSight.exe (Main application)")
    print("- GrainSight/ (Application dependencies)")
    print("- Install_GrainSight.bat (System installer)")
    print("- Launch_GrainSight.bat (Quick launcher)")
    print("- README.txt (Basic instructions)")
    print("- User_Guide.txt (Complete documentation)")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
