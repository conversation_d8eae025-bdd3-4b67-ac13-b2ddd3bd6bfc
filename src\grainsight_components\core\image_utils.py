# core/image_utils.py

import logging
import cv2
import numpy as np
import torch
from PIL import Image, ImageDraw
from typing import List, Tuple, Union, Optional

logger = logging.getLogger(__name__)

def _convert_tensor_to_numpy_optimized(mask_tensor: torch.Tensor, index: int, is_large_dataset: bool) -> Optional[np.ndarray]:
    """
    Optimized tensor-to-numpy conversion with efficient memory management.

    Args:
        mask_tensor: The tensor to convert
        index: Index for logging purposes
        is_large_dataset: Whether we're processing a large dataset

    Returns:
        numpy array or None if conversion failed
    """
    try:
        # For small datasets or CPU tensors, use direct conversion
        if not is_large_dataset or not mask_tensor.is_cuda:
            if mask_tensor.dtype == torch.bool:
                return mask_tensor.cpu().numpy().astype(np.uint8)
            elif mask_tensor.dtype.is_floating_point:
                return (mask_tensor.cpu().numpy() > 0.5).astype(np.uint8)
            elif mask_tensor.dtype == torch.uint8:
                return mask_tensor.cpu().numpy()
            else:
                logger.warning(f"Unsupported tensor dtype {mask_tensor.dtype} for annotation {index}")
                return None

        # For large datasets with CUDA tensors, use memory-efficient approach
        try:
            # Try direct conversion first
            if mask_tensor.dtype == torch.bool:
                return mask_tensor.cpu().numpy().astype(np.uint8)
            elif mask_tensor.dtype.is_floating_point:
                return (mask_tensor.cpu().numpy() > 0.5).astype(np.uint8)
            elif mask_tensor.dtype == torch.uint8:
                return mask_tensor.cpu().numpy()
        except RuntimeError as e:
            if "CUDA error: out of memory" in str(e):
                logger.debug(f"Using chunked conversion for tensor {index} due to memory constraints")
                return _chunked_tensor_conversion(mask_tensor, index)
            else:
                raise

    except Exception as e:
        logger.error(f"Failed to convert tensor {index}: {e}")
        return None

def _chunked_tensor_conversion(mask_tensor: torch.Tensor, index: int) -> Optional[np.ndarray]:
    """
    Memory-efficient chunked tensor conversion for large tensors.
    """
    try:
        mask_np = np.zeros(mask_tensor.shape, dtype=np.uint8)
        chunk_size = max(1, mask_tensor.shape[0] // 20)  # Adaptive chunk size
        height = mask_tensor.shape[0]

        for row in range(0, height, chunk_size):
            end_row = min(row + chunk_size, height)
            chunk = mask_tensor[row:end_row].cpu()

            if mask_tensor.dtype == torch.bool:
                mask_np[row:end_row] = chunk.numpy().astype(np.uint8)
            elif mask_tensor.dtype.is_floating_point:
                mask_np[row:end_row] = (chunk.numpy() > 0.5).astype(np.uint8)
            elif mask_tensor.dtype == torch.uint8:
                mask_np[row:end_row] = chunk.numpy()

            # Clear chunk from memory
            del chunk

        return mask_np
    except Exception as e:
        logger.error(f"Chunked conversion failed for tensor {index}: {e}")
        return None

def _draw_contours_optimized(mask_np: np.ndarray, draw: ImageDraw.ImageDraw,
                           contour_color_rgba: Tuple[int, int, int, int],
                           contour_thickness: int) -> int:
    """
    Optimized contour drawing with efficient contour processing.

    Returns:
        Number of contours drawn (0 or 1)
    """
    try:
        # Find external contours with optimized parameters
        contours, _ = cv2.findContours(mask_np, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if not contours:
            return 0

        # Draw only the largest contour for performance
        contour = max(contours, key=cv2.contourArea)

        # Optimize contour point processing
        if len(contour) < 3:  # Need at least 3 points for a meaningful contour
            return 0

        # Flatten contour points efficiently
        contour_flat = contour.squeeze().flatten().tolist()

        if len(contour_flat) >= 4:  # Need at least 2 points (4 coords)
            # Draw closed contour
            draw.line(contour_flat + contour_flat[:2], fill=contour_color_rgba, width=contour_thickness)
            return 1

        return 0
    except Exception as e:
        logger.debug(f"Error drawing contour: {e}")
        return 0

def create_segmented_visualization(
    base_image: Image.Image,
    annotations: Union[List[torch.Tensor], torch.Tensor],
    contour_thickness: int = 1,
    contour_color: Tuple[int, int, int] = (255, 255, 0) # Yellow contours default
    ) -> Optional[Image.Image]:
    """
    Creates an optimized visual representation with contours drawn on the base image.

    This optimized version:
    - Uses efficient memory management for large datasets
    - Implements progressive processing for better performance
    - Provides robust error handling and fallback mechanisms
    - Minimizes GPU-CPU transfers

    Args:
        base_image (PIL.Image): The original base image.
        annotations (Union[List[torch.Tensor], torch.Tensor]):
            List or Tensor of mask tensors (binary, H, W, uint8 or bool).
            Expected to be on CPU or GPU, will be moved to CPU.
        contour_thickness (int): Thickness of the contour lines.
        contour_color (tuple): RGB tuple for the contour color (e.g., (255, 255, 0)).

    Returns:
        Optional[PIL.Image.Image]: The base image with contours drawn on top (RGBA format).
                                   Returns None if base_image is None or annotations are empty/invalid.
                                   Returns the original image (converted to RGBA) if no valid contours found.
    """
    # Performance tracking
    import time
    start_time = time.time()

    # Early validation
    if base_image is None:
        logger.error("Base image is None, cannot create visualization.")
        return None

    # Determine number of annotations early for optimization decisions
    if isinstance(annotations, torch.Tensor):
        num_annotations = annotations.shape[0]
    elif isinstance(annotations, list):
        num_annotations = len(annotations)
    else:
        num_annotations = 0

    if num_annotations == 0:
        logger.info("No annotations provided, returning base image (RGBA).")
        try:
            return base_image.copy().convert("RGBA")
        except Exception as e:
            logger.error(f"Failed to convert base image to RGBA: {e}")
            return None

    # Optimize memory management based on dataset size
    is_large_dataset = num_annotations > 500
    if is_large_dataset:
        logger.info(f"Processing large dataset with {num_annotations} annotations - using optimized approach")
        # Clear GPU cache for large datasets
        if torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
                logger.debug("Cleared CUDA cache for large dataset processing")
            except Exception as e:
                logger.warning(f"Failed to clear CUDA cache: {e}")

    # Convert base image to RGBA with error handling
    try:
        vis_image_rgba = base_image.copy().convert("RGBA")
    except Exception as e:
        logger.error(f"Failed to convert base image to RGBA: {e}")
        return None

    # Create a transparent overlay to draw contours onto
    overlay = Image.new('RGBA', vis_image_rgba.size, (0, 0, 0, 0)) # Fully transparent
    draw = ImageDraw.Draw(overlay)

    logger.info(f"Creating contour visualization for {num_annotations} annotations.")

    # Define contour color with full alpha
    contour_color_rgba = contour_color + (255,) # Ensure full opacity for contours
    contours_drawn = 0

    # Batch process annotations for better performance
    failed_conversions = 0
    batch_size = 50 if is_large_dataset else num_annotations

    for batch_start in range(0, num_annotations, batch_size):
        batch_end = min(batch_start + batch_size, num_annotations)

        # Process batch
        for i in range(batch_start, batch_end):
            try:
                # Get mask tensor
                if isinstance(annotations, torch.Tensor):
                    mask_tensor = annotations[i]
                else:
                    mask_tensor = annotations[i]

                if mask_tensor is None:
                    continue # Skip if None

                # Validate tensor
                if not isinstance(mask_tensor, torch.Tensor) or mask_tensor.ndim != 2:
                    logger.warning(f"Skipping annotation {i}: invalid tensor (not 2D)")
                    failed_conversions += 1
                    continue

                # Optimized tensor-to-numpy conversion
                mask_np = _convert_tensor_to_numpy_optimized(mask_tensor, i, is_large_dataset)
                if mask_np is None:
                    failed_conversions += 1
                    continue

                # Skip empty masks early
                if np.sum(mask_np) == 0:
                    continue

                # Find and draw contours efficiently
                contours_drawn += _draw_contours_optimized(mask_np, draw, contour_color_rgba, contour_thickness)

            except Exception as e:
                logger.error(f"Error processing annotation {i}: {e}")
                failed_conversions += 1
                continue

        # Memory cleanup for large datasets
        if is_large_dataset and torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
            except Exception:
                pass

    # Log performance statistics
    elapsed_time = time.time() - start_time
    if failed_conversions > 0:
        logger.warning(f"Failed to process {failed_conversions}/{num_annotations} annotations")
    logger.info(f"Visualization created in {elapsed_time:.2f}s: {contours_drawn}/{num_annotations} contours drawn")

    if contours_drawn == 0:
        logger.warning("No valid contours were drawn.")
        # Return the base image converted to RGBA without overlay
        return vis_image_rgba

    # Composite the overlay (containing only contours) onto the original image
    # alpha_composite requires both images to be RGBA
    try:
        final_image = Image.alpha_composite(vis_image_rgba, overlay)
        logger.info(f"Contour visualization created with {contours_drawn} contours in {elapsed_time:.2f}s.")
        return final_image
    except Exception as comp_e:
        logger.error(f"Failed to composite contour overlay: {comp_e}")
        # Fallback: return the base image (already converted to RGBA)
        return vis_image_rgba