# gui/utils.py

import sys
import os
import json
import logging
import numpy as np
from PIL import Image
from typing import Optional

from PySide6 import QtCore, QtGui, QtWidgets
from PySide6.QtCore import Qt
from PySide6.QtGui import QImage, QPixmap, QPalette, QColor, QFont, QFontDatabase
from PySide6.QtWidgets import QApplication

logger = logging.getLogger(__name__)

# --- Resource Handling ---

def resource_path(relative_path: str) -> str:
    """ Get absolute path to resource, works for dev and for PyInstaller """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
        logger.debug(f"Running frozen (PyInstaller), _MEIPASS: {base_path}")
    except AttributeError:
        # Not frozen, determine path relative to this file's location
        # Assuming utils.py is in gui/, assets/ is a sibling of gui/ or inside gui/
        # Let's assume assets/ is inside gui/ for simplicity here based on structure
        base_path = os.path.dirname(os.path.abspath(__file__))
        # Construct path to assets directory relative to utils.py
        # base_path = os.path.join(base_path, 'assets') # Use this if assets is inside gui
        # If assets is a sibling of gui:
        base_path = os.path.join(os.path.dirname(base_path), 'gui', 'assets')


    # Check if the calculated base_path exists, provide fallback if needed
    if not os.path.isdir(base_path):
        logger.warning(f"Calculated base path does not exist: {base_path}")
        # Fallback strategy: check relative to CWD or provide a default
        alt_base_path = os.path.join(os.getcwd(), 'gui', 'assets')
        if os.path.isdir(alt_base_path):
             logger.warning(f"Falling back to CWD relative assets path: {alt_base_path}")
             base_path = alt_base_path
        else:
             logger.error("Cannot determine resource base path.")
             # Return relative path as last resort, may fail
             return relative_path

    final_path = os.path.join(base_path, relative_path)
    logger.debug(f"Resource requested: '{relative_path}', Resolved path: '{final_path}'")

    # Optional: Check if the final path exists
    # if not os.path.exists(final_path):
    #      logger.warning(f"Resource file does not exist: {final_path}")

    return final_path

# --- Image Conversion ---

def pil_image_to_qimage(pil_image: Image.Image) -> Optional[QImage]:
    """
    Converts a PIL Image to a QImage, handling common modes.
    """
    if pil_image is None:
        return None
    try:
        img_width, img_height = pil_image.size
        mode = pil_image.mode
        logger.debug(f"Converting PIL Image: Mode={mode}, Size=({img_width}x{img_height})")

        if mode == "RGB":
            byte_string = pil_image.tobytes("raw", "RGB")
            qimage = QImage(byte_string, img_width, img_height, img_width * 3, QImage.Format_RGB888)
            # Return RGB directly, QPixmap handles display byte order usually
            return qimage # .rgbSwapped() # Swapping might be needed depending on platform/Qt version

        elif mode == "RGBA":
            # Convert RGBA -> BGRA for ARGB32 format
            try:
                rgba_bytes = pil_image.tobytes("raw", "RGBA")
                arr_rgba = np.frombuffer(rgba_bytes, dtype=np.uint8).reshape((img_height, img_width, 4))
                arr_bgra = arr_rgba[..., [2, 1, 0, 3]] # B, G, R, A
                qimage = QImage(arr_bgra.tobytes(), img_width, img_height, img_width * 4, QImage.Format_ARGB32)
                return qimage
            except Exception as e:
                logger.error(f"Error during RGBA->BGRA conversion: {e}", exc_info=True)
                # Fallback to RGBA8888 if conversion fails
                logger.warning("Falling back to Format_RGBA8888 due to BGRA conversion error.")
                byte_string = pil_image.tobytes("raw", "RGBA")
                qimage = QImage(byte_string, img_width, img_height, img_width * 4, QImage.Format_RGBA8888)
                return qimage

        elif mode == "L": # Grayscale
            byte_string = pil_image.tobytes("raw", "L")
            qimage = QImage(byte_string, img_width, img_height, img_width * 1, QImage.Format_Grayscale8)
            # Or convert to RGB for display consistency:
            # return pil_image_to_qimage(pil_image.convert("RGB"))
            return qimage

        elif mode == "1": # Bilevel (black and white)
             # Convert to grayscale first
             return pil_image_to_qimage(pil_image.convert("L"))

        elif mode == "P": # Palette
             # Convert to RGBA to handle transparency properly
             return pil_image_to_qimage(pil_image.convert("RGBA"))

        else: # Attempt conversion via RGBA for other modes
            logger.warning(f"Unsupported PIL mode {mode}, attempting conversion via RGBA.")
            try:
                converted_image = pil_image.convert("RGBA")
                return pil_image_to_qimage(converted_image)
            except Exception as conv_e:
                logger.error(f"Could not convert PIL image mode {mode} to RGBA: {conv_e}", exc_info=True)
                return None

    except Exception as e:
        logger.exception(f"General error converting PIL image to QImage: {e}")
        return None


# --- Theme Handling ---

def define_light_theme() -> QPalette:
    """Defines the QPalette for the light theme."""
    palette = QPalette()
    # Define light theme colors (as previously provided)
    palette.setColor(QPalette.ColorRole.Window, QColor(240, 240, 240))
    palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.black)
    palette.setColor(QPalette.ColorRole.Base, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(220, 220, 220))
    palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.black)
    palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.black)
    palette.setColor(QPalette.ColorRole.Button, QColor(230, 230, 230))
    palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.black)
    palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.white)
    return palette

def define_dark_theme() -> QPalette:
    """Defines the QPalette for the dark theme."""
    palette = QPalette()
    # Define dark theme colors (as previously provided)
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Base, QColor(42, 42, 42))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(66, 66, 66))
    palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Button, QColor(66, 66, 66))
    palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.white)
    return palette


# Define stylesheets as string constants (or load from files)
LIGHT_STYLESHEET = """
    QMainWindow { background-color: #f0f0f0; }
    QDialog { background-color: #f0f0f0; }
    QGroupBox { border: 1px solid #c0c0c0; margin-top: 10px; padding-top: 5px; }
    QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top left; left: 7px; padding: 0px 5px; color: black; }
    QLabel, QRadioButton, QCheckBox { color: black; }
    QPushButton { background-color: #e6e6e6; color: black; border: 1px solid #adadad; padding: 5px 10px; min-height: 16px; border-radius: 3px; }
    QPushButton:hover { background-color: #f0f0f0; }
    QPushButton:pressed { background-color: #cccccc; }
    QPushButton:disabled { background-color: #d3d3d3; color: #a0a0a0; }
    QSlider::groove:horizontal { border: 1px solid #bbb; background: white; height: 8px; border-radius: 4px; }
    QSlider::sub-page:horizontal { background: #2a82da; border: 1px solid #777; height: 8px; border-radius: 4px; }
    QSlider::add-page:horizontal { background: #e0e0e0; border: 1px solid #777; height: 8px; border-radius: 4px; }
    QSlider::handle:horizontal { background: #e0e0e0; border: 1px solid #777; width: 14px; margin: -4px 0; border-radius: 7px; }
    QTreeView { background-color: white; color: black; alternate-background-color: #e8f4fc; border: 1px solid #c0c0c0; }
    QTreeView::item:selected { background-color: #2a82da; color: white; }
    QHeaderView::section { background-color: #e6e6e6; color: black; padding: 4px; border: 1px solid #c0c0c0; }
    QLineEdit, QSpinBox { background-color: white; color: black; border: 1px solid #c0c0c0; padding: 3px; }
    QStatusBar { color: black; }
    QMenu { background-color: white; border: 1px solid #c0c0c0; }
    QMenu::item { padding: 4px 20px; }
    QMenu::item:selected { background-color: #2a82da; color: white; }
    QToolBar { background-color: #e8e8e8; border: none; spacing: 3px; }
    QToolButton { padding: 3px; color: black; background-color: transparent; border: none; }
    QToolButton:hover { background-color: #d0d0d0; }
    QToolButton:pressed { background-color: #b0b0b0; }
    QProgressBar { border: 1px solid #bbb; background-color: white; text-align: center; color: black; }
    QProgressBar::chunk { background-color: #2a82da; width: 10px; margin: 0.5px; }
"""

DARK_STYLESHEET = """
    QMainWindow, QDialog { background-color: #353535; color: #e0e0e0; }
    QGroupBox { border: 1px solid #6a6a6a; margin-top: 10px; padding-top: 5px; color: #e0e0e0; }
    QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top left; left: 7px; padding: 0px 5px; color: #17a2b8; font-weight: bold; }
    QLabel, QRadioButton, QCheckBox { color: #e0e0e0; }
    QPushButton { background-color: #4a4a4a; color: #e0e0e0; border: 1px solid #7a7a7a; padding: 5px 10px; min-height: 16px; border-radius: 3px; }
    QPushButton:hover { background-color: #5a5a5a; }
    QPushButton:pressed { background-color: #6a6a6a; }
    QPushButton:disabled { background-color: #404040; color: #707070; border-color: #555555; }
    QSlider::groove:horizontal { border: 1px solid #505050; background: #2a2a2a; height: 8px; border-radius: 4px; }
    QSlider::sub-page:horizontal { background: #5070a0; border: 1px solid #5e5e5e; height: 8px; border-radius: 4px; }
    QSlider::add-page:horizontal { background: #383838; border: 1px solid #5e5e5e; height: 8px; border-radius: 4px; }
    QSlider::handle:horizontal { background: #5a5a5a; border: 1px solid #777777; width: 14px; margin: -4px 0; border-radius: 7px; }
    QTreeView { background-color: #2a2a2a; color: #e0e0e0; alternate-background-color: #383838; border: 1px solid #6a6a6a; }
    QTreeView::item:selected { background-color: #5070a0; color: white; }
    QHeaderView::section { background-color: #424242; padding: 4px; border: 1px solid #6a6a6a; color: #e0e0e0; }
    QLineEdit, QSpinBox { background-color: #383838; color: #e0e0e0; border: 1px solid #6a6a6a; padding: 3px; }
    QStatusBar { color: #e0e0e0; background-color: #353535; }
    QStatusBar::item { border: none; }
    QMenu { background-color: #353535; border: 1px solid #505050; color: #e0e0e0; }
    QMenu::item { padding: 4px 20px; }
    QMenu::item:selected { background-color: #5070a0; color: white; }
    QToolBar { background-color: #3f3f3f; border: none; spacing: 3px; }
    QToolButton { padding: 4px; color: #e0e0e0; background-color: transparent; border: none; margin: 1px; }
    QToolButton[text]:not([text=""]) { padding: 5px 8px; background-color: #4a4a4a; border: 1px solid #7a7a7a; border-radius: 3px; }
    QToolButton:hover { background-color: #5a5a5a; }
    QToolButton:pressed { background-color: #6a6a6a; }
    QToolButton:disabled { color: #707070; background-color: #404040; }
    QProgressBar { border: 1px solid #6a6a6a; background-color: #2a2a2a; text-align: center; color: #e0e0e0; border-radius: 3px; }
    QProgressBar::chunk { background-color: #5070a0; width: 10px; margin: 0.5px; border-radius: 2px; }
"""


def apply_stylesheet(widget: QtWidgets.QWidget, theme: str):
    """Applies the appropriate stylesheet based on the theme name."""
    if theme == 'dark':
        widget.setStyleSheet(DARK_STYLESHEET)
    else: # Default to light
        widget.setStyleSheet(LIGHT_STYLESHEET)

def detect_system_theme() -> str:
    """Detects the system theme (dark or light) based on OS settings."""
    try:
        # For Qt 6.5+, we can use QStyleHints to detect the system color scheme
        from PySide6.QtGui import QGuiApplication
        from PySide6.QtCore import Qt
        
        # Check if the Qt version supports ColorScheme
        if hasattr(QGuiApplication.styleHints(), 'colorScheme'):
            color_scheme = QGuiApplication.styleHints().colorScheme()
            if color_scheme == Qt.ColorScheme.Dark:
                logger.info("System theme detected: dark")
                return 'dark'
            else:
                logger.info("System theme detected: light")
                return 'light'
        else:
            logger.info("Qt version does not support system theme detection via ColorScheme")
    except Exception as e:
        logger.error(f"Error detecting system theme: {e}")
    
    # Fallback to default
    logger.info("Using default theme: light")
    return 'light'

def load_theme_preference(theme_file_path: str) -> str:
    """Loads the theme preference from a JSON file.
    If theme is set to 'system', it will detect the system theme.
    """
    default_theme = 'system'  # Changed default to 'system'
    if os.path.exists(theme_file_path):
        try:
            with open(theme_file_path, 'r') as f:
                data = json.load(f)
                theme = data.get('theme', default_theme)
                
                # If theme is set to 'system', detect the system theme
                if theme == 'system':
                    theme = detect_system_theme()
                elif theme not in ['light', 'dark', 'system']:
                    theme = default_theme
                    
                logger.info(f"Loaded theme preference: {theme}")
                return theme
        except Exception as e:
            logger.error(f"Failed to load theme file {theme_file_path}: {e}")
            return detect_system_theme()  # Fallback to system theme
    else:
        logger.info(f"Theme preference file not found ({theme_file_path}), using system theme.")
        return detect_system_theme()

def save_theme_preference(theme_file_path: str, theme: str):
    """Saves the theme preference to a JSON file."""
    if theme not in ['light', 'dark']:
        logger.warning(f"Attempted to save invalid theme: {theme}. Saving 'light'.")
        theme = 'light'
    data = {'theme': theme}
    try:
        os.makedirs(os.path.dirname(theme_file_path), exist_ok=True) # Ensure config dir exists
        with open(theme_file_path, 'w') as f:
            json.dump(data, f, indent=4)
        logger.info(f"Saved theme preference: {theme} to {theme_file_path}")
    except Exception as e:
        logger.error(f"Failed to save theme preference to {theme_file_path}: {e}")


# --- Font Handling ---

def find_system_font(font_name: str) -> Optional[str]:
    """ Tries to find a font path using matplotlib's font manager or system specifics. """
    try:
        import matplotlib.font_manager
        prop = matplotlib.font_manager.FontProperties(family=font_name)
        font_path = matplotlib.font_manager.findfont(prop, fallback_to_default=True)
        if font_path:
            logger.debug(f"Found font '{font_name}' (or fallback) via matplotlib: {font_path}")
            return font_path
        else:
             logger.warning(f"Font '{font_name}' not found using matplotlib.")
             return None
    except ImportError:
        logger.warning("Matplotlib font manager not available. Using basic OS check.")
        # Basic OS check (less reliable)
        if sys.platform == 'win32':
            fonts_dir = os.path.join(os.environ.get('WINDIR', 'C:\\Windows'), 'Fonts')
            common_exts = [".ttf", ".otf", ".ttc"]
        elif sys.platform == 'darwin':
            fonts_dir = '/Library/Fonts/' # System fonts
            user_fonts_dir = os.path.expanduser('~/Library/Fonts/')
            common_exts = [".ttf", ".otf", ".ttc", ".dfont"]
        else: # Linux approximation
            fonts_dir = '/usr/share/fonts/' # Common location
            user_fonts_dir = os.path.expanduser('~/.fonts/')
            common_exts = [".ttf", ".otf"]

        search_dirs = [fonts_dir]
        if 'user_fonts_dir' in locals() and os.path.isdir(user_fonts_dir):
             search_dirs.append(user_fonts_dir)

        for directory in search_dirs:
             if not os.path.isdir(directory): continue
             for ext in common_exts:
                 # Simple check for font name variations
                 variations = [font_name, font_name.lower(), font_name.capitalize(), font_name.replace(" ", "")]
                 for var in variations:
                      fpath = os.path.join(directory, var + ext)
                      if os.path.exists(fpath):
                           logger.debug(f"Found potential font: {fpath}")
                           return fpath
        logger.warning(f"Font '{font_name}' not found in common system directories.")
        return None
    except Exception as e:
        logger.error(f"Error finding font '{font_name}': {e}")
        return None

def load_font(font_path: str) -> bool:
    """Loads a font using QFontDatabase."""
    if not font_path or not os.path.exists(font_path):
         logger.error(f"Font path invalid or file not found: {font_path}")
         return False
    font_id = QFontDatabase.addApplicationFont(font_path)
    if font_id == -1:
        logger.warning(f"Failed to load font from {font_path}")
        return False
    font_families = QFontDatabase.applicationFontFamilies(font_id)
    if not font_families:
        logger.warning(f"Loaded font from {font_path}, but couldn't retrieve families.")
        # Font might still be usable by exact filename? Treat as success for now.
        # return False
    logger.info(f"Successfully loaded font: {font_families[0] if font_families else 'Unknown Family'} from {font_path}")
    return True