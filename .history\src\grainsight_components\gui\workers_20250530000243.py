# gui/workers.py
import logging
import os
import time
from typing import List, Tu<PERSON>, Dict, Any, Optional

import numpy as np
import torch
from PIL import Image
from PySide6.QtCore import QObject, Signal, QThread

# Attempt to import segmentation functions, handle ImportError if core components are missing
try:
    from ..segmentation_utils import segment_image, segment_image_with_points, segment_image_with_box
    from ..postprocessing import (calculate_grain_properties, filter_grains_by_area, 
                                 filter_grains_by_circularity, filter_grains_by_aspect_ratio,
                                 filter_grains_by_solidity, apply_watershed_segmentation)
    from ..visualization_utils import create_segmented_visualization
except ImportError as e:
    # This allows the GUI to run for development even if core logic is missing
    # You might want to log this or display a more user-friendly message
    print(f"Core components not found (likely a development setup): {e}")
    # Define dummy functions or classes if needed for the GUI to load
    def segment_image(*args, **kwargs): raise NotImplementedError("Core segmentation missing")
    def segment_image_with_points(*args, **kwargs): raise NotImplementedError("Core segmentation missing")
    def segment_image_with_box(*args, **kwargs): raise NotImplementedError("Core segmentation missing")
    def calculate_grain_properties(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def filter_grains_by_area(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def filter_grains_by_circularity(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def filter_grains_by_aspect_ratio(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def filter_grains_by_solidity(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def apply_watershed_segmentation(*args, **kwargs): raise NotImplementedError("Core postprocessing missing")
    def create_segmented_visualization(*args, **kwargs): raise NotImplementedError("Core image utils missing")

logger = logging.getLogger(__name__) # Ensure this is at the top level after imports

# --- Constants ---

# --- Patch Processing Functions ---

def generate_patch_coordinates(image_width: int, image_height: int, rows: int, cols: int, overlap: float):
    """
    Generate patch coordinates for dividing an image into overlapping patches.
    
    Args:
        image_width: Width of the full image
        image_height: Height of the full image
        rows: Number of rows to divide the image into
        cols: Number of columns to divide the image into
        overlap: Overlap ratio (0.0 to 1.0)
    
    Yields:
        Tuple[int, int, int, int]: (x_start, y_start, patch_width, patch_height)
    """
    # Calculate patch dimensions with overlap
    patch_width = int(image_width / cols * (1 + overlap))
    patch_height = int(image_height / rows * (1 + overlap))
    
    # Calculate step sizes (distance between patch centers)
    step_x = image_width // cols
    step_y = image_height // rows
    
    for row in range(rows):
        for col in range(cols):
            # Calculate patch position
            x_start = max(0, col * step_x - int(patch_width * overlap / 2))
            y_start = max(0, row * step_y - int(patch_height * overlap / 2))
            
            # Ensure patch doesn't exceed image boundaries
            x_end = min(image_width, x_start + patch_width)
            y_end = min(image_height, y_start + patch_height)
            
            # Adjust patch dimensions to fit within image
            actual_width = x_end - x_start
            actual_height = y_end - y_start
            
            yield (x_start, y_start, actual_width, actual_height)

def merge_patch_results(patch_results: List[Tuple], full_image_size: Tuple[int, int], device: torch.device, 
                       nms_iou_threshold: float = 0.5, **kwargs):
    """
    Merge segmentation results from multiple patches using simple NMS-based approach.
    
    Args:
        patch_results: List of (annotations_tensor, (x_offset, y_offset)) tuples
        full_image_size: (height, width) of the full image
        device: PyTorch device to use
        nms_iou_threshold: IoU threshold for non-maximum suppression
        **kwargs: Additional parameters (ignored for compatibility)
    
    Returns:
        torch.Tensor: Merged annotations tensor
    """
    logger.debug(f"Starting merge_patch_results. nms_iou_threshold: {nms_iou_threshold}") # Added logging
    if not patch_results:
        logger.debug("No patch results to merge.") # Added logging
        return torch.zeros((0, full_image_size[0], full_image_size[1]), dtype=torch.bool, device=device)
    
    full_height, full_width = full_image_size
    merged_masks = []
    
    # Collect all masks and transform them to full image coordinates
    logger.debug(f"Processing {len(patch_results)} patch results.") # Added logging
    for idx, (annotations, (x_offset, y_offset)) in enumerate(patch_results):
        if annotations is None or len(annotations) == 0:
            logger.debug(f"Patch {idx+1} has no annotations.") # Added logging
            continue
            
        # Ensure annotations are on the correct device
        if isinstance(annotations, torch.Tensor):
            annotations = annotations.to(device)
        
        logger.debug(f"Patch {idx+1} has {len(annotations)} annotations. Offset: ({x_offset}, {y_offset})") # Added logging
        # Transform each mask to full image coordinates
        for i, mask in enumerate(annotations):
            # Create full-size mask
            full_mask = torch.zeros((full_height, full_width), dtype=torch.bool, device=device)
            
            # Get patch dimensions
            patch_h, patch_w = mask.shape[-2:]
            
            # Calculate the region in the full image where this patch belongs
            y_end = min(full_height, y_offset + patch_h)
            x_end = min(full_width, x_offset + patch_w)
            
            # Copy the mask to the appropriate region
            full_mask[y_offset:y_end, x_offset:x_end] = mask[:y_end-y_offset, :x_end-x_offset]
            
            merged_masks.append(full_mask)
    
    if not merged_masks:
        logger.debug("No masks collected after transforming to full image coordinates.") # Added logging
        return torch.zeros((0, full_height, full_width), dtype=torch.bool, device=device)
    
    logger.debug(f"Collected {len(merged_masks)} masks in total before NMS.") # Added logging
    # Stack all masks
    all_masks = torch.stack(merged_masks)
    
    # Simple duplicate removal based on IoU
    keep_indices = []
    logger.debug(f"Performing NMS with threshold: {nms_iou_threshold}") # Added logging
    for i in range(len(all_masks)):
        keep = True
        for j_idx, j in enumerate(keep_indices):
            # Calculate IoU between masks
            intersection = torch.logical_and(all_masks[i], all_masks[j]).sum().float()
            union = torch.logical_or(all_masks[i], all_masks[j]).sum().float()
            iou = intersection / (union + 1e-8)
            logger.debug(f"  IoU between mask {i} and kept mask {j_idx} (original index {j}): {iou:.4f}") # Added logging
            
            if iou > nms_iou_threshold:
                logger.debug(f"    Mask {i} overlaps with kept mask {j} (IoU: {iou:.4f} > {nms_iou_threshold}). Discarding mask {i}.") # Added logging
                keep = False
                break
        
        if keep:
            logger.debug(f"  Keeping mask {i}.") # Added logging
            keep_indices.append(i)
    
    logger.debug(f"Kept {len(keep_indices)} masks after NMS.") # Added logging
    # Return filtered masks
    if keep_indices:
        final_masks = all_masks[keep_indices]
        logger.debug(f"Final merged masks shape: {final_masks.shape}") # Added logging
        return final_masks
    else:
        logger.debug("No masks kept after NMS.") # Added logging
        return torch.zeros((0, full_height, full_width), dtype=torch.bool, device=device)

# --- Worker Classes ---

class ProcessingWorker(QObject):
    """
    Worker object to perform segmentation and analysis in a background thread.
    """
    # Signals emitted by the worker
    finished = Signal(object, object, object)  # (df_results, annotations_tensor, visualization_image)
    progress = Signal(int)  # Percentage 0-100
    error = Signal(str)     # Error message string
    status = Signal(str)    # Status update message string

    def __init__(self, pil_image: Image.Image, model: object, device: torch.device,
                 scale_factor: float, params: dict):
        super().__init__()
        self.pil_image = pil_image
        self.model = model
        self.device = device
        self.scale_factor = scale_factor
        self.params = params
        self._is_running = True

    def stop(self):
        """Signals the worker to stop processing."""
        logger.info("Processing worker received stop signal.")
        self._is_running = False

    @Slot()
    def run(self):
        """Executes the segmentation and parameter calculation."""
        try:
            start_time = time.time()
            model_type = self.params.get('model_type', 'unknown')
            logger.info(f"Worker started processing with {model_type.upper()} model.")

            if not self._is_running:
                return
            
            self.status.emit("Starting segmentation...")
            self.progress.emit(5)

            # Move model to device
            try:
                model_on_device = self.model.to(self.device)
                if hasattr(model_on_device, 'eval'):
                    model_on_device.eval()
                logger.debug("Model moved to device and set to eval mode.")
            except Exception as model_move_e:
                logger.error(f"Failed to move model to device {self.device}: {model_move_e}")
                self.error.emit(f"Error moving model to {self.device}: {model_move_e}")
                self.finished.emit(None, None, None)
                return

            # Segmentation
            try:
                raw_results, annotations_full_res = segment_image(
                    self.pil_image, model_on_device, self.device, **self.params
                )
            finally:
                # Move model back to CPU if it was on GPU
                if self.device.type == 'cuda':
                    try:
                        self.model.to('cpu')
                        torch.cuda.empty_cache()
                    except Exception as e:
                        logger.warning(f"Error moving model back to CPU: {e}")

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            if annotations_full_res is None or len(annotations_full_res) == 0:
                logger.warning("No objects detected in the image.")
                self.error.emit("No objects detected in the image.")
                self.finished.emit(None, None, None)
                return

            self.status.emit(f"Found {len(annotations_full_res)} objects. Calculating parameters...")
            self.progress.emit(50)

            # Parameter calculation
            df, valid_mask = calculate_parameters(annotations_full_res, self.scale_factor, self.progress.emit)

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            if df is None or df.empty:
                logger.warning("No valid objects found after parameter calculation.")
                self.error.emit("No valid objects found after parameter calculation.")
                self.finished.emit(None, None, None)
                return

            # Filter annotations
            valid_indices = df.index.to_list()
            final_annotations = annotations_full_res[valid_indices]

            # Create visualization
            self.status.emit(f"Creating visualization for {len(df)} objects...")
            self.progress.emit(90)

            contour_thickness = self.params.get('contour_thickness', 1)
            segmented_image_vis = create_segmented_visualization(
                self.pil_image, final_annotations, contour_thickness=contour_thickness
            )

            # Finish
            end_time = time.time()
            processing_time = end_time - start_time
            self.progress.emit(100)
            self.status.emit(f"Processing complete: {len(df)} objects in {processing_time:.2f}s")
            logger.info(f"Worker finished in {processing_time:.2f} seconds with {len(df)} objects.")
            self.finished.emit(df, final_annotations, segmented_image_vis)

        except Exception as e:
            logger.exception("Error occurred in processing worker:")
            self.error.emit(f"Processing error: {e}")
            self.finished.emit(None, None, None)

class PatchProcessingWorker(QObject):
    """
    Worker object for patch-based segmentation and analysis.
    """
    # Signals
    finished = Signal(object, object, object)  # df, annotations_tensor, vis_image
    progress = Signal(int)
    error = Signal(str)
    status = Signal(str)

    def __init__(self, pil_image: Image.Image, model: object, device: torch.device,
                 scale_factor: float, seg_params: dict, patch_config: dict):
        super().__init__()
        self.pil_image = pil_image
        self.model = model
        self.device = device
        self.scale_factor = scale_factor
        self.seg_params = seg_params
        self.patch_config = patch_config
        self._is_running = True

    def stop(self):
        logger.info("Patch processing worker received stop signal.")
        self._is_running = False

    @Slot()
    def run(self):
        """Executes patch-based segmentation, merging, and analysis."""
        try:
            start_time = time.time()
            model_type = self.seg_params.get('model_type', 'unknown')
            logger.info(f"Patch worker started processing with {model_type.upper()} model.")
            logger.info(f"Patch Config: {self.patch_config}")
            logger.info(f"Using device: {self.device.type}")

            full_width, full_height = self.pil_image.size
            patch_results = []

            # Move model to device
            try:
                model_on_device = self.model.to(self.device)
                if hasattr(model_on_device, 'eval'):
                    model_on_device.eval()
                logger.debug("Model moved to device for patch processing.")
            except Exception as model_move_e:
                self.error.emit(f"Error moving model to {self.device}: {model_move_e}")
                self.finished.emit(None, None, None)
                return

            # Generate and process patches
            try:
                patch_coords = list(generate_patch_coordinates(
                    full_width, full_height,
                    self.patch_config['rows'],
                    self.patch_config['cols'],
                    self.patch_config['overlap']
                ))

                total_patches = len(patch_coords)
                logger.info(f"Processing {total_patches} patches...")
                self.status.emit(f"Processing {total_patches} patches...")

                # Process each patch
                for i, (x_start, y_start, patch_w, patch_h) in enumerate(patch_coords):
                    if not self._is_running:
                        break

                    # Update progress
                    progress_pct = int((i / total_patches) * 70)  # Use 0-70% for patch processing
                    self.progress.emit(progress_pct)
                    self.status.emit(f"Processing patch {i+1}/{total_patches}...")

                    # Extract patch from image
                    patch_box = (x_start, y_start, x_start + patch_w, y_start + patch_h)
                    patch_img = self.pil_image.crop(patch_box)

                    # Process patch with segmentation model
                    logger.debug(f"Segmenting patch {i+1}/{total_patches} at ({x_start},{y_start})")
                    try:
                        # Map and filter seg_params to match segment_image function signature
                        filtered_params = {}
                        for k, v in self.seg_params.items():
                            if k == 'iou':
                                filtered_params['iou_threshold'] = v  # Map 'iou' to 'iou_threshold'
                            elif k == 'conf':
                                filtered_params['conf_threshold'] = v  # Map 'conf' to 'conf_threshold'
                            elif k in ['input_size', 'max_det', 'points_per_side', 'pred_iou_thresh', 
                                      'stability_score_thresh', 'box_nms_thresh']:
                                filtered_params[k] = v
                            # Skip 'model_type', 'contour_thickness' and other incompatible params
                        
                        _, patch_annotations = segment_image(
                            patch_img, model_on_device, self.device, **filtered_params
                        )

                        # Store results with offset information
                        if patch_annotations is not None and len(patch_annotations) > 0:
                            patch_results.append((patch_annotations, (x_start, y_start)))
                            logger.debug(f"Patch {i+1} yielded {len(patch_annotations)} annotations")
                        else:
                            logger.debug(f"No objects detected in patch {i+1}")

                    except Exception as patch_e:
                        logger.error(f"Error processing patch {i+1}: {patch_e}")
                        continue

            finally:
                # Clean up model resources
                if self.device.type == 'cuda':
                    try:
                        self.model.to('cpu')
                        torch.cuda.empty_cache()
                    except Exception as e:
                        logger.warning(f"Error moving model back to CPU: {e}")

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            # Merge patch results
            self.status.emit("Merging patch results...")
            self.progress.emit(75)

            if not patch_results:
                logger.warning("No valid annotations found in any patch.")
                self.error.emit("No objects detected in any patch.")
                self.finished.emit(None, None, None)
                return

            try:
                merged_annotations = merge_patch_results(
                    patch_results,
                    (full_height, full_width),
                    self.device,
                    nms_iou_threshold=self.seg_params.get('iou_threshold', 0.5)
                )

                if merged_annotations.shape[0] == 0:
                    logger.warning("No annotations remained after merging patches.")
                    self.error.emit("No valid objects found after merging patches.")
                    self.finished.emit(None, None, None)
                    return

                logger.info(f"Successfully merged patches: {merged_annotations.shape[0]} objects found.")
                self.status.emit(f"Found {merged_annotations.shape[0]} objects. Calculating parameters...")

            except Exception as merge_e:
                logger.exception(f"Error merging patch results: {merge_e}")
                self.error.emit(f"Error merging patch results: {merge_e}")
                self.finished.emit(None, None, None)
                return

            # Calculate parameters
            self.progress.emit(80)
            df, valid_mask = calculate_parameters(merged_annotations, self.scale_factor, self.progress.emit)

            if not self._is_running:
                self.finished.emit(None, None, None)
                return

            if df is None or df.empty:
                logger.warning("No valid objects found after parameter calculation.")
                self.error.emit("No valid objects found after parameter calculation.")
                self.finished.emit(None, None, None)
                return

            # Filter annotations to match DataFrame
            try:
                valid_indices = df.index.to_list()
                final_annotations = merged_annotations[valid_indices]
                logger.info(f"Filtered to {len(df)} valid objects after parameter calculation.")
            except Exception as filter_e:
                logger.exception(f"Error filtering merged annotations: {filter_e}")
                self.error.emit(f"Error filtering results: {filter_e}")
                self.finished.emit(None, None, None)
                return

            # Create visualization
            self.status.emit(f"Creating visualization for {len(df)} objects...")
            self.progress.emit(95)

            contour_thickness = self.seg_params.get('contour_thickness', 1)
            segmented_image_vis = create_segmented_visualization(
                self.pil_image,
                final_annotations,
                contour_thickness=contour_thickness
            )

            # Finish
            end_time = time.time()
            processing_time = end_time - start_time
            self.progress.emit(100)
            self.status.emit(f"Patch processing complete: {len(df)} objects in {processing_time:.2f}s")
            logger.info(f"Patch worker finished in {processing_time:.2f} seconds with {len(df)} objects.")
            self.finished.emit(df, final_annotations, segmented_image_vis)

        except Exception as e:
            logger.exception("Error occurred in patch processing worker:")
            self.error.emit(f"Patch processing error: {e}")
            self.finished.emit(None, None, None)